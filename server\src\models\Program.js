const mongoose = require('mongoose');

const ProgramSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Program name is required'],
    trim: true,
    maxlength: [200, 'Program name cannot exceed 200 characters'],
  },
  code: {
    type: String,
    required: [true, 'Program code is required'],
    unique: true,
    uppercase: true,
    trim: true,
    maxlength: [20, 'Program code cannot exceed 20 characters'],
    match: [/^[A-Z0-9\-]+$/, 'Program code must contain only uppercase letters, numbers, and hyphens'],
  },
  deptId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
    required: [true, 'Department is required'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
  },
  duration: {
    years: {
      type: Number,
      required: [true, 'Program duration in years is required'],
      min: [1, 'Duration must be at least 1 year'],
      max: [10, 'Duration cannot exceed 10 years'],
    },
    semesters: {
      type: Number,
      required: [true, 'Total semesters is required'],
      min: [2, 'Must have at least 2 semesters'],
      max: [20, 'Cannot exceed 20 semesters'],
    },
  },
  level: {
    type: String,
    enum: {
      values: ['UG', 'PG', 'PhD', 'Diploma', 'Certificate'],
      message: 'Level must be one of: UG, PG, PhD, Diploma, Certificate',
    },
    required: [true, 'Program level is required'],
  },
  // Program Outcomes (POs)
  pos: [{
    code: {
      type: String,
      required: true,
      trim: true,
      maxlength: [10, 'PO code cannot exceed 10 characters'],
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: [1000, 'PO description cannot exceed 1000 characters'],
    },
    category: {
      type: String,
      enum: ['Knowledge', 'Skills', 'Attitude'],
      default: 'Knowledge',
    },
  }],
  // Program Specific Outcomes (PSOs)
  psos: [{
    code: {
      type: String,
      required: true,
      trim: true,
      maxlength: [10, 'PSO code cannot exceed 10 characters'],
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: [1000, 'PSO description cannot exceed 1000 characters'],
    },
  }],
  // NBA Accreditation Cycle
  nbaCycle: {
    startYear: {
      type: Number,
      required: [true, 'NBA cycle start year is required'],
      min: [2000, 'Start year must be after 2000'],
    },
    endYear: {
      type: Number,
      required: [true, 'NBA cycle end year is required'],
      validate: {
        validator: function(value) {
          return value > this.nbaCycle.startYear;
        },
        message: 'End year must be after start year',
      },
    },
    status: {
      type: String,
      enum: ['Pending', 'In Progress', 'Completed', 'Renewed'],
      default: 'Pending',
    },
    accreditationGrade: {
      type: String,
      enum: ['A++', 'A+', 'A', 'B++', 'B+', 'B', 'C', 'Not Accredited'],
    },
  },
  // Academic regulations
  regulations: {
    passingMarks: {
      type: Number,
      default: 40,
      min: [0, 'Passing marks cannot be negative'],
      max: [100, 'Passing marks cannot exceed 100'],
    },
    maxCredits: {
      type: Number,
      required: [true, 'Maximum credits is required'],
      min: [1, 'Maximum credits must be at least 1'],
    },
    gradeSystem: {
      type: String,
      enum: ['10-point', '4-point', 'Percentage'],
      default: '10-point',
    },
  },
  // Admission details
  admission: {
    intake: {
      type: Number,
      required: [true, 'Admission intake is required'],
      min: [1, 'Intake must be at least 1'],
    },
    eligibility: {
      type: String,
      trim: true,
      maxlength: [500, 'Eligibility criteria cannot exceed 500 characters'],
    },
    entranceExam: {
      type: String,
      trim: true,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes (code index is automatically created by unique: true)
ProgramSchema.index({ deptId: 1 });
ProgramSchema.index({ level: 1 });
ProgramSchema.index({ isActive: 1 });
ProgramSchema.index({ 'nbaCycle.startYear': 1, 'nbaCycle.endYear': 1 });

// Virtual for department details
ProgramSchema.virtual('department', {
  ref: 'Department',
  localField: 'deptId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for courses count
ProgramSchema.virtual('coursesCount', {
  ref: 'Course',
  localField: '_id',
  foreignField: 'programId',
  count: true,
});

// Virtual for current academic year
ProgramSchema.virtual('currentAcademicYear').get(function() {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // 0-indexed
  
  // Assuming academic year starts in July (month 7)
  if (currentMonth >= 7) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
});

// Static method to find by code
ProgramSchema.statics.findByCode = function(code) {
  return this.findOne({ code: code.toUpperCase() });
};

// Static method to get active programs by department
ProgramSchema.statics.getActiveByDepartment = function(deptId) {
  return this.find({ deptId, isActive: true }).populate('department');
};

// Static method to get programs by level
ProgramSchema.statics.getByLevel = function(level) {
  return this.find({ level, isActive: true }).populate('department');
};

// Instance method to get program statistics
ProgramSchema.methods.getStatistics = async function() {
  const Course = mongoose.model('Course');
  
  const [coursesCount, currentYearCourses] = await Promise.all([
    Course.countDocuments({ programId: this._id }),
    Course.countDocuments({ 
      programId: this._id, 
      year: new Date().getFullYear() 
    }),
  ]);

  return {
    coursesCount,
    currentYearCourses,
    totalPOs: this.pos.length,
    totalPSOs: this.psos.length,
    nbaCycleStatus: this.nbaCycle.status,
    isActive: this.isActive,
  };
};

// Instance method to validate PO/PSO codes
ProgramSchema.methods.validateOutcomeCodes = function() {
  const poCodes = this.pos.map(po => po.code);
  const psoCodes = this.psos.map(pso => pso.code);
  
  // Check for duplicate PO codes
  const duplicatePOs = poCodes.filter((code, index) => poCodes.indexOf(code) !== index);
  
  // Check for duplicate PSO codes
  const duplicatePSOs = psoCodes.filter((code, index) => psoCodes.indexOf(code) !== index);
  
  return {
    isValid: duplicatePOs.length === 0 && duplicatePSOs.length === 0,
    duplicatePOs,
    duplicatePSOs,
  };
};

module.exports = mongoose.model('Program', ProgramSchema);
