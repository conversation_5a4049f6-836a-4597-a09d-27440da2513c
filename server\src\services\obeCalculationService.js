const Course = require('../models/Course');
const Assessment = require('../models/Assessment');
const Result = require('../models/Result');
const OBEConfig = require('../models/OBEConfig');
const IndirectSurvey = require('../models/IndirectSurvey');
const Program = require('../models/Program');

class OBECalculationService {
  /**
   * Calculate CO attainment for a specific course
   * @param {string} courseId - Course ID
   * @param {string} academicYear - Academic year (e.g., "2023-2024")
   * @returns {Object} CO attainment data
   */
  async calculateCourseAttainment(courseId, academicYear) {
    try {
      const course = await Course.findById(courseId).populate('program');
      if (!course) {
        throw new Error('Course not found');
      }

      const config = await OBEConfig.getByProgram(course.programId) || 
                    { ...OBEConfig.getDefaultConfig(), programId: course.programId };

      // Get all assessments for the course
      const assessments = await Assessment.find({
        courseId,
        isActive: true,
        status: { $in: ['Evaluated', 'Completed'] }
      });

      // Calculate direct attainment
      const directAttainment = await this.calculateDirectAttainment(
        courseId, 
        assessments, 
        config
      );

      // Calculate indirect attainment
      const indirectAttainment = await this.calculateIndirectAttainment(
        courseId, 
        academicYear, 
        config
      );

      // Combine direct and indirect attainment
      const finalCOAttainment = this.combineCOAttainment(
        directAttainment, 
        indirectAttainment, 
        config
      );

      // Calculate PO/PSO attainment from CO attainment
      const poAttainment = await this.calculatePOAttainment(
        course, 
        finalCOAttainment, 
        config
      );

      const psoAttainment = await this.calculatePSOAttainment(
        course, 
        finalCOAttainment, 
        config
      );

      return {
        courseId,
        courseName: course.title,
        courseCode: course.code,
        academicYear,
        coAttainment: finalCOAttainment,
        poAttainment,
        psoAttainment,
        directAttainment,
        indirectAttainment,
        config: {
          directWeight: config.directWeight,
          indirectWeight: config.indirectWeight,
          targets: config.targets
        },
        calculatedAt: new Date()
      };

    } catch (error) {
      throw new Error(`CO attainment calculation failed: ${error.message}`);
    }
  }

  /**
   * Calculate direct attainment from assessment results
   */
  async calculateDirectAttainment(courseId, assessments, config) {
    const directAttainment = {};

    for (const assessment of assessments) {
      // Get all results for this assessment
      const results = await Result.find({
        assessmentId: assessment._id,
        status: { $ne: 'Absent' }
      });

      if (results.length === 0) continue;

      // Calculate CO-wise performance for this assessment
      const assessmentCOPerformance = this.calculateAssessmentCOPerformance(
        assessment, 
        results
      );

      // Weight by assessment type
      const assessmentWeight = config.assessmentWeights[assessment.type] || 0.1;

      // Aggregate into overall direct attainment
      Object.keys(assessmentCOPerformance).forEach(coKey => {
        if (!directAttainment[coKey]) {
          directAttainment[coKey] = {
            totalWeightedScore: 0,
            totalWeight: 0,
            assessments: []
          };
        }

        const coPerformance = assessmentCOPerformance[coKey];
        directAttainment[coKey].totalWeightedScore += coPerformance.percentage * assessmentWeight;
        directAttainment[coKey].totalWeight += assessmentWeight;
        directAttainment[coKey].assessments.push({
          assessmentId: assessment._id,
          assessmentTitle: assessment.title,
          assessmentType: assessment.type,
          percentage: coPerformance.percentage,
          weight: assessmentWeight
        });
      });
    }

    // Calculate final direct attainment percentages
    const finalDirectAttainment = {};
    Object.keys(directAttainment).forEach(coKey => {
      const co = directAttainment[coKey];
      finalDirectAttainment[coKey] = {
        percentage: co.totalWeight > 0 ? (co.totalWeightedScore / co.totalWeight) : 0,
        assessments: co.assessments,
        totalWeight: co.totalWeight
      };
    });

    return finalDirectAttainment;
  }

  /**
   * Calculate CO performance for a single assessment
   */
  calculateAssessmentCOPerformance(assessment, results) {
    const coPerformance = {};

    // Initialize CO performance tracking
    assessment.coTags.forEach(tag => {
      coPerformance[`CO${tag.coIndex}`] = {
        totalMarks: tag.marks,
        obtainedMarks: 0,
        studentCount: 0,
        weight: tag.weight
      };
    });

    // Aggregate student performance
    results.forEach(result => {
      result.coPerformance.forEach(coPerfData => {
        const coKey = `CO${coPerfData.coIndex}`;
        if (coPerformance[coKey]) {
          coPerformance[coKey].obtainedMarks += coPerfData.obtainedMarks;
          coPerformance[coKey].studentCount++;
        }
      });
    });

    // Calculate percentages
    Object.keys(coPerformance).forEach(coKey => {
      const co = coPerformance[coKey];
      if (co.studentCount > 0 && co.totalMarks > 0) {
        const avgObtained = co.obtainedMarks / co.studentCount;
        co.percentage = (avgObtained / co.totalMarks) * 100;
      } else {
        co.percentage = 0;
      }
    });

    return coPerformance;
  }

  /**
   * Calculate indirect attainment from surveys
   */
  async calculateIndirectAttainment(courseId, academicYear, config) {
    const surveys = await IndirectSurvey.find({
      courseId,
      academicYear,
      status: { $in: ['Closed', 'Analyzed'] },
      isActive: true
    });

    const indirectAttainment = {};

    for (const survey of surveys) {
      const surveyWeight = config.indirectAssessment[`${survey.respondentType.toLowerCase()}SurveyWeight`] || 0;

      survey.responses.forEach(response => {
        const coKey = `CO${response.coIndex}`;
        
        if (!indirectAttainment[coKey]) {
          indirectAttainment[coKey] = {
            totalWeightedScore: 0,
            totalWeight: 0,
            surveys: []
          };
        }

        const normalizedScore = response.statistics.normalizedScore * 100; // Convert to percentage
        indirectAttainment[coKey].totalWeightedScore += normalizedScore * surveyWeight;
        indirectAttainment[coKey].totalWeight += surveyWeight;
        indirectAttainment[coKey].surveys.push({
          surveyId: survey._id,
          respondentType: survey.respondentType,
          normalizedScore,
          weight: surveyWeight,
          responseCount: response.statistics.totalResponses
        });
      });
    }

    // Calculate final indirect attainment percentages
    const finalIndirectAttainment = {};
    Object.keys(indirectAttainment).forEach(coKey => {
      const co = indirectAttainment[coKey];
      finalIndirectAttainment[coKey] = {
        percentage: co.totalWeight > 0 ? (co.totalWeightedScore / co.totalWeight) : 0,
        surveys: co.surveys,
        totalWeight: co.totalWeight
      };
    });

    return finalIndirectAttainment;
  }

  /**
   * Combine direct and indirect attainment
   */
  combineCOAttainment(directAttainment, indirectAttainment, config) {
    const combinedAttainment = {};

    // Get all unique CO keys
    const allCOKeys = new Set([
      ...Object.keys(directAttainment),
      ...Object.keys(indirectAttainment)
    ]);

    allCOKeys.forEach(coKey => {
      const direct = directAttainment[coKey] || { percentage: 0 };
      const indirect = indirectAttainment[coKey] || { percentage: 0 };

      const finalPercentage = (direct.percentage * config.directWeight) + 
                             (indirect.percentage * config.indirectWeight);

      combinedAttainment[coKey] = {
        finalPercentage: Math.round(finalPercentage * 100) / 100,
        directPercentage: Math.round(direct.percentage * 100) / 100,
        indirectPercentage: Math.round(indirect.percentage * 100) / 100,
        target: config.targets?.coTarget || 70,
        attained: finalPercentage >= (config.targets?.coTarget || 70),
        attainmentLevel: this.getAttainmentLevel(finalPercentage, config),
        directWeight: config.directWeight,
        indirectWeight: config.indirectWeight
      };
    });

    return combinedAttainment;
  }

  /**
   * Calculate PO attainment from CO attainment
   */
  async calculatePOAttainment(course, coAttainment, config) {
    const poAttainment = {};

    // Initialize PO tracking
    course.program.pos.forEach(po => {
      poAttainment[po.code] = {
        description: po.description,
        category: po.category,
        mappedCOs: [],
        totalWeightedScore: 0,
        totalWeight: 0
      };
    });

    // Process CO-PO mappings
    course.mapping.po.forEach(mapping => {
      const coKey = `CO${mapping.coIndex}`;
      const coData = coAttainment[coKey];

      if (coData && poAttainment[mapping.poCode]) {
        const levelWeight = config.levelWeights[`L${mapping.level}`] || 1;
        const effectiveWeight = mapping.weight * levelWeight;

        poAttainment[mapping.poCode].mappedCOs.push({
          coIndex: mapping.coIndex,
          coPercentage: coData.finalPercentage,
          mappingLevel: mapping.level,
          mappingWeight: mapping.weight,
          levelWeight,
          effectiveWeight
        });

        poAttainment[mapping.poCode].totalWeightedScore += coData.finalPercentage * effectiveWeight;
        poAttainment[mapping.poCode].totalWeight += effectiveWeight;
      }
    });

    // Calculate final PO percentages
    Object.keys(poAttainment).forEach(poCode => {
      const po = poAttainment[poCode];
      po.finalPercentage = po.totalWeight > 0 ? 
        Math.round((po.totalWeightedScore / po.totalWeight) * 100) / 100 : 0;
      po.target = config.targets?.poTarget || 70;
      po.attained = po.finalPercentage >= po.target;
      po.attainmentLevel = this.getAttainmentLevel(po.finalPercentage, config);
    });

    return poAttainment;
  }

  /**
   * Calculate PSO attainment from CO attainment
   */
  async calculatePSOAttainment(course, coAttainment, config) {
    const psoAttainment = {};

    // Initialize PSO tracking
    course.program.psos.forEach(pso => {
      psoAttainment[pso.code] = {
        description: pso.description,
        mappedCOs: [],
        totalWeightedScore: 0,
        totalWeight: 0
      };
    });

    // Process CO-PSO mappings
    course.mapping.pso.forEach(mapping => {
      const coKey = `CO${mapping.coIndex}`;
      const coData = coAttainment[coKey];

      if (coData && psoAttainment[mapping.psoCode]) {
        const levelWeight = config.levelWeights[`L${mapping.level}`] || 1;
        const effectiveWeight = mapping.weight * levelWeight;

        psoAttainment[mapping.psoCode].mappedCOs.push({
          coIndex: mapping.coIndex,
          coPercentage: coData.finalPercentage,
          mappingLevel: mapping.level,
          mappingWeight: mapping.weight,
          levelWeight,
          effectiveWeight
        });

        psoAttainment[mapping.psoCode].totalWeightedScore += coData.finalPercentage * effectiveWeight;
        psoAttainment[mapping.psoCode].totalWeight += effectiveWeight;
      }
    });

    // Calculate final PSO percentages
    Object.keys(psoAttainment).forEach(psoCode => {
      const pso = psoAttainment[psoCode];
      pso.finalPercentage = pso.totalWeight > 0 ? 
        Math.round((pso.totalWeightedScore / pso.totalWeight) * 100) / 100 : 0;
      pso.target = config.targets?.psoTarget || 70;
      pso.attained = pso.finalPercentage >= pso.target;
      pso.attainmentLevel = this.getAttainmentLevel(pso.finalPercentage, config);
    });

    return psoAttainment;
  }

  /**
   * Calculate program-level PO/PSO attainment
   */
  async calculateProgramAttainment(programId, academicYear) {
    try {
      const program = await Program.findById(programId);
      if (!program) {
        throw new Error('Program not found');
      }

      const config = await OBEConfig.getByProgram(programId) || 
                    { ...OBEConfig.getDefaultConfig(), programId };

      // Get all courses for the program in the academic year
      const courses = await Course.find({
        programId,
        year: this.getYearFromAcademicYear(academicYear),
        isActive: true
      });

      const programPOAttainment = {};
      const programPSOAttainment = {};
      const courseAttainments = [];

      // Initialize program PO/PSO tracking
      program.pos.forEach(po => {
        programPOAttainment[po.code] = {
          description: po.description,
          category: po.category,
          courses: [],
          totalWeightedScore: 0,
          totalCredits: 0
        };
      });

      program.psos.forEach(pso => {
        programPSOAttainment[pso.code] = {
          description: pso.description,
          courses: [],
          totalWeightedScore: 0,
          totalCredits: 0
        };
      });

      // Calculate attainment for each course and aggregate
      for (const course of courses) {
        const courseAttainment = await this.calculateCourseAttainment(course._id, academicYear);
        courseAttainments.push(courseAttainment);

        const courseCredits = course.credits.total;

        // Aggregate PO attainment
        Object.keys(courseAttainment.poAttainment).forEach(poCode => {
          if (programPOAttainment[poCode]) {
            const poData = courseAttainment.poAttainment[poCode];
            programPOAttainment[poCode].courses.push({
              courseId: course._id,
              courseCode: course.code,
              courseTitle: course.title,
              credits: courseCredits,
              percentage: poData.finalPercentage
            });
            programPOAttainment[poCode].totalWeightedScore += poData.finalPercentage * courseCredits;
            programPOAttainment[poCode].totalCredits += courseCredits;
          }
        });

        // Aggregate PSO attainment
        Object.keys(courseAttainment.psoAttainment).forEach(psoCode => {
          if (programPSOAttainment[psoCode]) {
            const psoData = courseAttainment.psoAttainment[psoCode];
            programPSOAttainment[psoCode].courses.push({
              courseId: course._id,
              courseCode: course.code,
              courseTitle: course.title,
              credits: courseCredits,
              percentage: psoData.finalPercentage
            });
            programPSOAttainment[psoCode].totalWeightedScore += psoData.finalPercentage * courseCredits;
            programPSOAttainment[psoCode].totalCredits += courseCredits;
          }
        });
      }

      // Calculate final program-level percentages
      Object.keys(programPOAttainment).forEach(poCode => {
        const po = programPOAttainment[poCode];
        po.finalPercentage = po.totalCredits > 0 ? 
          Math.round((po.totalWeightedScore / po.totalCredits) * 100) / 100 : 0;
        po.target = config.targets?.poTarget || 70;
        po.attained = po.finalPercentage >= po.target;
        po.attainmentLevel = this.getAttainmentLevel(po.finalPercentage, config);
      });

      Object.keys(programPSOAttainment).forEach(psoCode => {
        const pso = programPSOAttainment[psoCode];
        pso.finalPercentage = pso.totalCredits > 0 ? 
          Math.round((pso.totalWeightedScore / pso.totalCredits) * 100) / 100 : 0;
        pso.target = config.targets?.psoTarget || 70;
        pso.attained = pso.finalPercentage >= pso.target;
        pso.attainmentLevel = this.getAttainmentLevel(pso.finalPercentage, config);
      });

      return {
        programId,
        programName: program.name,
        programCode: program.code,
        academicYear,
        poAttainment: programPOAttainment,
        psoAttainment: programPSOAttainment,
        courseAttainments,
        totalCourses: courses.length,
        calculatedAt: new Date()
      };

    } catch (error) {
      throw new Error(`Program attainment calculation failed: ${error.message}`);
    }
  }

  /**
   * Get attainment level based on percentage and config
   */
  getAttainmentLevel(percentage, config) {
    if (!config.attainmentBands || config.attainmentBands.length === 0) {
      // Default bands
      if (percentage >= 90) return 'Excellent';
      if (percentage >= 80) return 'Very Good';
      if (percentage >= 70) return 'Good';
      if (percentage >= 60) return 'Satisfactory';
      if (percentage >= 50) return 'Needs Improvement';
      return 'Not Attained';
    }

    const band = config.attainmentBands.find(band => 
      percentage >= band.min && percentage <= band.max
    );
    return band ? band.label : 'Not Classified';
  }

  /**
   * Extract year from academic year string
   */
  getYearFromAcademicYear(academicYear) {
    return parseInt(academicYear.split('-')[0]);
  }
}

module.exports = new OBECalculationService();
