const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://dbUser:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

// User schema (simplified)
const UserSchema = new mongoose.Schema({
  name: String,
  email: { type: String, unique: true, lowercase: true },
  passwordHash: String,
  role: { type: String, default: 'FACULTY' },
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
  refreshToken: String,
}, { timestamps: true });

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('passwordHash')) return next();
  try {
    const salt = await bcrypt.genSalt(12);
    this.passwordHash = await bcrypt.hash(this.passwordHash, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.passwordHash);
};

// Static method to find by email
UserSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

const User = mongoose.model('User', UserSchema);

async function createAdmin() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findByEmail('<EMAIL>');
    if (existingAdmin) {
      console.log('ℹ️  Admin user already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
      return;
    }

    // Create admin user
    const admin = new User({
      name: 'System Administrator',
      email: '<EMAIL>',
      passwordHash: 'admin123', // Will be hashed by pre-save middleware
      role: 'ADMIN',
      isActive: true
    });

    await admin.save();
    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('🎉 You can now login with:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: ADMIN');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

createAdmin();
