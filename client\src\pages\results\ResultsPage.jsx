import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { resultsAPI, assessmentsAPI, coursesAPI } from '../../services/api';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ResultCard } from '../../components/results/ResultCard';
import { ResultModal } from '../../components/results/ResultModal';
import { BulkUploadModal } from '../../components/results/BulkUploadModal';
import { COAnalysisModal } from '../../components/results/COAnalysisModal';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  ArrowUpTrayIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

export const ResultsPage = () => {
  const { user } = useAuthStore();
  const [results, setResults] = useState([]);
  const [assessments, setAssessments] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedAssessment, setSelectedAssessment] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalResults, setTotalResults] = useState(0);

  // Modal states
  const [showResultModal, setShowResultModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [showCOAnalysisModal, setShowCOAnalysisModal] = useState(false);
  const [selectedResult, setSelectedResult] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'

  const resultStatuses = ['Pass', 'Fail', 'Absent', 'Malpractice'];

  // Fetch results with filters
  const fetchResults = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 12,
        ...(searchTerm && { studentRoll: searchTerm }),
        ...(selectedCourse && { courseId: selectedCourse }),
        ...(selectedAssessment && { assessmentId: selectedAssessment }),
        ...(selectedStatus && { status: selectedStatus }),
      };

      const response = await resultsAPI.getResults(params);
      setResults(response.data.data || []);
      setTotalPages(response.data.meta?.pagination?.totalPages || 1);
      setTotalResults(response.data.meta?.pagination?.totalItems || 0);
    } catch (error) {
      console.error('Error fetching results:', error);
      toast.error('Failed to fetch results');
    } finally {
      setLoading(false);
    }
  };

  // Fetch courses for filter dropdown
  const fetchCourses = async () => {
    try {
      const response = await coursesAPI.getCourses({
        facultyId: user._id,
        limit: 100
      });
      setCourses(response.data.data || []);
    } catch (error) {
      console.error('Error fetching courses:', error);
    }
  };

  // Fetch assessments for filter dropdown
  const fetchAssessments = async () => {
    try {
      const params = {
        facultyId: user._id,
        limit: 100,
        ...(selectedCourse && { courseId: selectedCourse }),
      };
      const response = await assessmentsAPI.getAssessments(params);
      setAssessments(response.data.data || []);
    } catch (error) {
      console.error('Error fetching assessments:', error);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    fetchAssessments();
  }, [selectedCourse]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    fetchResults();
  }, [currentPage, searchTerm, selectedCourse, selectedAssessment, selectedStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle result creation/editing
  const handleResultSubmit = async (resultData) => {
    try {
      if (modalMode === 'create') {
        await resultsAPI.createResult(resultData);
        toast.success('Result created successfully');
      } else {
        await resultsAPI.updateResult(selectedResult._id, resultData);
        toast.success('Result updated successfully');
      }
      setShowResultModal(false);
      setSelectedResult(null);
      fetchResults();
    } catch (error) {
      console.error('Error saving result:', error);
      toast.error(error.response?.data?.message || 'Failed to save result');
    }
  };

  // Handle bulk upload
  const handleBulkUpload = async (uploadData) => {
    try {
      const response = await resultsAPI.bulkUploadResults(uploadData);
      toast.success(`Bulk upload completed: ${response.data.data.successfulUploads} results uploaded`);
      setShowBulkUploadModal(false);
      fetchResults();

      // Show detailed results if there were errors
      if (response.data.data.errors > 0) {
        toast.error(`${response.data.data.errors} errors occurred during upload. Check the details.`);
      }
    } catch (error) {
      console.error('Error bulk uploading results:', error);
      toast.error(error.response?.data?.message || 'Failed to upload results');
    }
  };

  // Handle result actions
  const handleEditResult = (result) => {
    setSelectedResult(result);
    setModalMode('edit');
    setShowResultModal(true);
  };

  const handleViewCOAnalysis = (result) => {
    setSelectedResult(result);
    setShowCOAnalysisModal(true);
  };

  const handleCreateResult = () => {
    setSelectedResult(null);
    setModalMode('create');
    setShowResultModal(true);
  };

  const handleBulkUploadClick = () => {
    setShowBulkUploadModal(true);
  };

  const handleDeleteResult = async (resultId) => {
    if (window.confirm('Are you sure you want to delete this result?')) {
      try {
        await resultsAPI.deleteResult(resultId);
        toast.success('Result deleted successfully');
        fetchResults();
      } catch (error) {
        console.error('Error deleting result:', error);
        toast.error(error.response?.data?.message || 'Failed to delete result');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Results Management</h1>
          <p className="mt-2 text-sm text-gray-700">
            Upload and manage student assessment results with CO-wise analysis.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleBulkUploadClick}
              className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <ArrowUpTrayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Bulk Upload
            </button>
            <button
              type="button"
              onClick={handleCreateResult}
              className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Add Result
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search by roll number..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          {/* Course Filter */}
          <div>
            <select
              value={selectedCourse}
              onChange={(e) => {
                setSelectedCourse(e.target.value);
                setSelectedAssessment(''); // Reset assessment when course changes
              }}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Courses</option>
              {courses.map((course) => (
                <option key={course._id} value={course._id}>
                  {course.code} - {course.title}
                </option>
              ))}
            </select>
          </div>

          {/* Assessment Filter */}
          <div>
            <select
              value={selectedAssessment}
              onChange={(e) => setSelectedAssessment(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Assessments</option>
              {assessments.map((assessment) => (
                <option key={assessment._id} value={assessment._id}>
                  {assessment.title} ({assessment.type})
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              {resultStatuses.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>

          {/* Clear Filters */}
          <div>
            <button
              type="button"
              onClick={() => {
                setSearchTerm('');
                setSelectedCourse('');
                setSelectedAssessment('');
                setSelectedStatus('');
                setCurrentPage(1);
              }}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FunnelIcon className="-ml-1 mr-2 h-4 w-4" aria-hidden="true" />
              Clear
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClipboardDocumentListIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Results</dt>
                  <dd className="text-lg font-medium text-gray-900">{totalResults}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pass</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {results.filter(r => r.status === 'Pass').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AcademicCapIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Fail</dt>
                  <dd className="text-lg font-medium text-red-600">
                    {results.filter(r => r.status === 'Fail').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pass Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {results.length > 0
                      ? `${Math.round((results.filter(r => r.status === 'Pass').length / results.length) * 100)}%`
                      : '0%'
                    }
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Results Grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      ) : results.length > 0 ? (
        <>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {results.map((result) => (
              <ResultCard
                key={result._id}
                result={result}
                onEdit={handleEditResult}
                onViewCOAnalysis={handleViewCOAnalysis}
                onDelete={handleDeleteResult}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{(currentPage - 1) * 12 + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * 12, totalResults)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{totalResults}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === page
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || selectedCourse || selectedAssessment || selectedStatus
              ? 'Try adjusting your search criteria.'
              : 'Get started by adding student results.'}
          </p>
          {!searchTerm && !selectedCourse && !selectedAssessment && !selectedStatus && (
            <div className="mt-6 flex justify-center space-x-3">
              <button
                type="button"
                onClick={handleBulkUploadClick}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowUpTrayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Bulk Upload
              </button>
              <button
                type="button"
                onClick={handleCreateResult}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Add Result
              </button>
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      {showResultModal && (
        <ResultModal
          isOpen={showResultModal}
          onClose={() => {
            setShowResultModal(false);
            setSelectedResult(null);
          }}
          onSubmit={handleResultSubmit}
          result={selectedResult}
          mode={modalMode}
          assessments={assessments}
        />
      )}

      {showBulkUploadModal && (
        <BulkUploadModal
          isOpen={showBulkUploadModal}
          onClose={() => setShowBulkUploadModal(false)}
          onSubmit={handleBulkUpload}
          assessments={assessments}
        />
      )}

      {showCOAnalysisModal && selectedResult && (
        <COAnalysisModal
          isOpen={showCOAnalysisModal}
          onClose={() => {
            setShowCOAnalysisModal(false);
            setSelectedResult(null);
          }}
          result={selectedResult}
        />
      )}
    </div>
  );
};
