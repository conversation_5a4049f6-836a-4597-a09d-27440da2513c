import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, ChartBarIcon } from '@heroicons/react/24/outline';

export const COAnalysisModal = ({ isOpen, onClose, result }) => {
  const [coAnalysis, setCoAnalysis] = useState([]);
  const [overallAnalysis, setOverallAnalysis] = useState(null);

  useEffect(() => {
    if (result && result.assessment) {
      calculateCOAnalysis();
    }
  }, [result]);

  const calculateCOAnalysis = () => {
    const assessment = result.assessment;
    const course = assessment.course;
    
    if (!assessment.questions || !course.cos) {
      return;
    }

    // Group questions by CO
    const coQuestions = {};
    assessment.questions.forEach(q => {
      if (!coQuestions[q.coIndex]) {
        coQuestions[q.coIndex] = [];
      }
      coQuestions[q.coIndex].push(q);
    });

    // Calculate CO-wise performance
    const coPerformance = [];
    
    Object.entries(coQuestions).forEach(([coIndex, questions]) => {
      const co = course.cos.find(c => c.index === parseInt(coIndex));
      if (!co) return;

      // Calculate total marks for this CO
      const totalMarks = questions.reduce((sum, q) => sum + q.marks, 0);
      
      // Calculate obtained marks for this CO from question-wise results
      let obtainedMarks = 0;
      if (result.questionWise) {
        questions.forEach(q => {
          const questionResult = result.questionWise.find(qw => qw.q === q.questionNumber);
          if (questionResult) {
            obtainedMarks += questionResult.marks;
          }
        });
      } else {
        // If no question-wise data, distribute proportionally
        const assessmentPercentage = result.obtained / assessment.maxMarks;
        obtainedMarks = totalMarks * assessmentPercentage;
      }

      const percentage = totalMarks > 0 ? (obtainedMarks / totalMarks) * 100 : 0;
      const target = co.target || 70;
      const attained = percentage >= target;

      coPerformance.push({
        coIndex: parseInt(coIndex),
        coStatement: co.statement,
        target,
        totalMarks,
        obtainedMarks,
        percentage,
        attained,
        questions: questions.length,
        bloomsLevel: co.bloomsLevel,
      });
    });

    setCoAnalysis(coPerformance);

    // Calculate overall analysis
    const totalCOs = coPerformance.length;
    const attainedCOs = coPerformance.filter(co => co.attained).length;
    const averagePerformance = totalCOs > 0 
      ? coPerformance.reduce((sum, co) => sum + co.percentage, 0) / totalCOs 
      : 0;

    setOverallAnalysis({
      totalCOs,
      attainedCOs,
      attainmentRate: totalCOs > 0 ? (attainedCOs / totalCOs) * 100 : 0,
      averagePerformance,
      overallGrade: result.grade,
      overallPercentage: result.percentage,
      status: result.status,
    });
  };

  const getPerformanceColor = (percentage, target) => {
    if (percentage >= target) return 'text-green-600';
    if (percentage >= target * 0.8) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBarColor = (percentage, target) => {
    if (percentage >= target) return 'bg-green-500';
    if (percentage >= target * 0.8) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getBloomsLevelColor = (level) => {
    const colors = {
      'Remember': 'bg-blue-100 text-blue-800',
      'Understand': 'bg-green-100 text-green-800',
      'Apply': 'bg-yellow-100 text-yellow-800',
      'Analyze': 'bg-orange-100 text-orange-800',
      'Evaluate': 'bg-red-100 text-red-800',
      'Create': 'bg-purple-100 text-purple-800',
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  if (!result) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      CO-wise Performance Analysis
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      {result.studentName} ({result.studentRoll}) - {result.assessment?.title}
                    </p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Overall Performance Summary */}
                  {overallAnalysis && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Overall Performance</h4>
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-900">
                            {overallAnalysis.overallPercentage?.toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-500">Overall Score</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {overallAnalysis.overallGrade}
                          </div>
                          <div className="text-sm text-gray-500">Grade</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {overallAnalysis.attainedCOs}/{overallAnalysis.totalCOs}
                          </div>
                          <div className="text-sm text-gray-500">COs Attained</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {overallAnalysis.attainmentRate?.toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-500">Attainment Rate</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* CO-wise Analysis */}
                  {coAnalysis.length > 0 ? (
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Course Outcome Analysis</h4>
                      <div className="space-y-4">
                        {coAnalysis.map((co) => (
                          <div key={co.coIndex} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h5 className="text-sm font-medium text-gray-900">
                                    CO{co.coIndex}
                                  </h5>
                                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getBloomsLevelColor(co.bloomsLevel)}`}>
                                    {co.bloomsLevel}
                                  </span>
                                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                    co.attained ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {co.attained ? 'Attained' : 'Not Attained'}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600 mb-2">{co.coStatement}</p>
                              </div>
                              <div className="text-right">
                                <div className={`text-lg font-semibold ${getPerformanceColor(co.percentage, co.target)}`}>
                                  {co.percentage.toFixed(1)}%
                                </div>
                                <div className="text-xs text-gray-500">
                                  Target: {co.target}%
                                </div>
                              </div>
                            </div>

                            {/* Performance Bar */}
                            <div className="mb-3">
                              <div className="flex justify-between text-xs text-gray-500 mb-1">
                                <span>Performance</span>
                                <span>{co.obtainedMarks.toFixed(1)}/{co.totalMarks} marks</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${getPerformanceBarColor(co.percentage, co.target)}`}
                                  style={{ width: `${Math.min(100, co.percentage)}%` }}
                                ></div>
                              </div>
                              {/* Target line */}
                              <div className="relative">
                                <div
                                  className="absolute top-0 w-0.5 h-2 bg-gray-600"
                                  style={{ left: `${co.target}%`, transform: 'translateY(-100%)' }}
                                ></div>
                              </div>
                            </div>

                            {/* Additional Details */}
                            <div className="grid grid-cols-1 gap-2 sm:grid-cols-3 text-xs text-gray-500">
                              <div>Questions: {co.questions}</div>
                              <div>Total Marks: {co.totalMarks}</div>
                              <div>Obtained: {co.obtainedMarks.toFixed(1)}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No CO Analysis Available</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        CO analysis requires question-wise mapping in the assessment.
                      </p>
                    </div>
                  )}

                  {/* Question-wise Performance */}
                  {result.questionWise && result.questionWise.length > 0 && (
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Question-wise Performance</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Question
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                CO
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Marks
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Percentage
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {result.questionWise.map((question, index) => {
                              const percentage = (question.marks / question.maxMarks) * 100;
                              return (
                                <tr key={index}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    Q{question.q}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    CO{question.coIndex}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {question.marks}/{question.maxMarks}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {percentage.toFixed(1)}%
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                      <div
                                        className={`h-2 rounded-full ${
                                          percentage >= 70 ? 'bg-green-500' :
                                          percentage >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                        }`}
                                        style={{ width: `${Math.min(100, percentage)}%` }}
                                      ></div>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  {overallAnalysis && overallAnalysis.attainmentRate < 70 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                      <h4 className="text-sm font-medium text-yellow-900 mb-2">Recommendations</h4>
                      <ul className="text-sm text-yellow-800 space-y-1">
                        {coAnalysis.filter(co => !co.attained).map(co => (
                          <li key={co.coIndex}>
                            • Focus on improving CO{co.coIndex} ({co.bloomsLevel} level) - Current: {co.percentage.toFixed(1)}%, Target: {co.target}%
                          </li>
                        ))}
                        {overallAnalysis.averagePerformance < 60 && (
                          <li>• Consider additional practice sessions and remedial classes</li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex justify-end pt-6 border-t border-gray-200 mt-6">
                  <button
                    type="button"
                    onClick={onClose}
                    className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
