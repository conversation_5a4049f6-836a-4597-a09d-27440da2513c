import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Link, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import {
  XMarkIcon,
  HomeIcon,
  UsersIcon,
  BuildingOfficeIcon,
  AcademicCapIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  FolderIcon,
  ChartPieIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'] },
  { name: 'Users', href: '/users', icon: UsersIcon, roles: ['ADMIN', 'DEPT_HEAD'] },
  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, roles: ['ADMIN'] },
  { name: 'Programs', href: '/programs', icon: AcademicCapIcon, roles: ['ADMIN', 'DEPT_HEAD'] },
  { name: 'Courses', href: '/courses', icon: BookOpenIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY'] },
  { name: 'Assessments', href: '/assessments', icon: ClipboardDocumentListIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY'] },
  { name: 'Results', href: '/results', icon: ChartBarIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY'] },
];

const obeNavigation = [
  { name: 'Attainment', href: '/obe/attainment', icon: ChartPieIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'] },
  { name: 'Configuration', href: '/obe/config', icon: CogIcon, roles: ['ADMIN', 'DEPT_HEAD'] },
];

const nbaNavigation = [
  { name: 'Readiness', href: '/nba/readiness', icon: ShieldCheckIcon, roles: ['ADMIN', 'DEPT_HEAD', 'AUDITOR'] },
  { name: 'Evidence', href: '/nba/evidence', icon: FolderIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'] },
];

const reportsNavigation = [
  { name: 'Reports', href: '/reports', icon: DocumentTextIcon, roles: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'] },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { user } = useAuthStore();

  const isCurrentPath = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const canAccessRoute = (roles) => {
    return roles.includes(user?.role);
  };

  const NavigationSection = ({ title, items }) => (
    <div className="space-y-1">
      {title && (
        <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          {title}
        </h3>
      )}
      {items
        .filter(item => canAccessRoute(item.roles))
        .map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={classNames(
              isCurrentPath(item.href)
                ? 'bg-blue-50 border-r-4 border-blue-600 text-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
              'group flex items-center px-3 py-2 text-sm font-medium'
            )}
            onClick={onClose}
          >
            <item.icon
              className={classNames(
                isCurrentPath(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500',
                'mr-3 flex-shrink-0 h-5 w-5'
              )}
              aria-hidden="true"
            />
            {item.name}
          </Link>
        ))}
    </div>
  );

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center flex-shrink-0 px-4 py-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">NBA</span>
            </div>
          </div>
          <div className="ml-3">
            <h1 className="text-lg font-semibold text-gray-900">OBE Suite</h1>
            <p className="text-xs text-gray-500">NBA Accreditation</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 pb-4 space-y-6 overflow-y-auto">
        <NavigationSection items={navigation} />
        <NavigationSection title="OBE Management" items={obeNavigation} />
        <NavigationSection title="NBA Management" items={nbaNavigation} />
        <NavigationSection title="Reports" items={reportsNavigation} />
      </nav>

      {/* User info */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-700 font-medium text-sm">
                {user?.name?.charAt(0)?.toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">{user?.name}</p>
            <p className="text-xs text-gray-500">{user?.role}</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button type="button" className="-m-2.5 p-2.5" onClick={onClose}>
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-2">
                  <SidebarContent />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6">
          <SidebarContent />
        </div>
      </div>
    </>
  );
};
