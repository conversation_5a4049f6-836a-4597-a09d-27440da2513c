{"name": "nba-obe-suite", "version": "1.0.0", "description": "NBA Accreditation Readiness & OBE Attainment Suite - A comprehensive MERN stack application for managing Outcome Based Education and NBA accreditation processes", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "client:preview": "cd client && npm run preview", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test", "lint": "cd server && npm run lint && cd ../client && npm run lint"}, "keywords": ["NBA", "OBE", "accreditation", "education", "MERN", "outcome-based-education"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}