const { body } = require('express-validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Evidence = require('../models/Evidence');
const Program = require('../models/Program');
const Course = require('../models/Course');
const Department = require('../models/Department');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads/evidence');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `evidence-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    // Allow common document and image formats
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
      'text/csv'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new AppError('File type not allowed', 400), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Validation rules
const createEvidenceValidation = [
  body('title')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Evidence title must be between 2 and 200 characters'),
  body('criterion')
    .isIn(['VisionMission', 'ProgramOutcomes', 'Curriculum', 'TeachingLearning', 'Infra', 'StudentPerformance', 'Faculty', 'Finance', 'ContinuousImprovement'])
    .withMessage('Invalid NBA criterion'),
  body('type')
    .isIn(['Document', 'Link', 'Image', 'Video', 'Data', 'Report'])
    .withMessage('Invalid evidence type'),
  body('accessLevel')
    .optional()
    .isIn(['Public', 'Internal', 'Restricted', 'Confidential'])
    .withMessage('Invalid access level'),
];

// Get all evidence with filtering and search
const getEvidence = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      criterion,
      type,
      verificationStatus,
      academicYear,
      programId,
      search,
    } = req.query;

    // Build filter object
    const filters = { isActive: true };
    
    if (criterion) filters.criterion = criterion;
    if (type) filters.type = type;
    if (verificationStatus) filters.verificationStatus = verificationStatus;
    if (academicYear) filters.academicYear = academicYear;
    if (programId) filters.relatedProgramId = programId;

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
      const programIds = programs.map(p => p._id);
      
      if (filters.relatedProgramId) {
        // Check if the specified program belongs to user's department
        if (!programIds.some(id => id.toString() === filters.relatedProgramId)) {
          return sendSuccess(res, 'Evidence retrieved successfully', [], {
            pagination: { currentPage: 1, totalPages: 0, totalItems: 0, itemsPerPage: parseInt(limit) }
          });
        }
      } else {
        filters.$or = [
          { relatedProgramId: { $in: programIds } },
          { relatedDepartmentId: req.user.deptId },
          { relatedProgramId: null, relatedDepartmentId: null } // General evidence
        ];
      }
    }

    const skip = (page - 1) * limit;

    let evidenceQuery;
    if (search) {
      evidenceQuery = Evidence.searchEvidence(search, filters);
    } else {
      evidenceQuery = Evidence.find(filters)
        .populate('relatedCourse', 'title code')
        .populate('relatedProgram', 'name code')
        .populate('relatedDepartment', 'name code')
        .populate('createdBy', 'name email')
        .populate('verifiedBy', 'name email')
        .sort({ createdAt: -1 });
    }

    const [evidence, total] = await Promise.all([
      evidenceQuery.skip(skip).limit(parseInt(limit)),
      Evidence.countDocuments(filters),
    ]);

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Evidence retrieved successfully', evidence, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get evidence by ID
const getEvidenceById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const evidence = await Evidence.findById(id)
      .populate('relatedCourse', 'title code semester year')
      .populate('relatedProgram', 'name code level')
      .populate('relatedDepartment', 'name code')
      .populate('createdBy', 'name email')
      .populate('verifiedBy', 'name email');

    if (!evidence) {
      return sendNotFound(res, 'Evidence not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN') {
      let hasAccess = false;

      if (evidence.relatedProgramId) {
        const program = await Program.findById(evidence.relatedProgramId);
        hasAccess = program && program.deptId.toString() === req.user.deptId.toString();
      } else if (evidence.relatedDepartmentId) {
        hasAccess = evidence.relatedDepartmentId.toString() === req.user.deptId.toString();
      } else {
        hasAccess = true; // General evidence accessible to all
      }

      if (!hasAccess) {
        return next(new AppError('Access denied', 403));
      }
    }

    // Increment view count
    await evidence.incrementViewCount();

    return sendSuccess(res, 'Evidence retrieved successfully', evidence);
  } catch (error) {
    return next(error);
  }
};

// Create new evidence
const createEvidence = async (req, res, next) => {
  try {
    const {
      title,
      description,
      criterion,
      subCriterion,
      type,
      link,
      tags,
      keywords,
      relatedCourseId,
      relatedProgramId,
      relatedDepartmentId,
      academicYear,
      semester,
      source,
      dateOfEvidence,
      validityPeriod,
      accessLevel,
      authorizedRoles,
    } = req.body;

    // Validate related entities
    if (relatedProgramId) {
      const program = await Program.findById(relatedProgramId);
      if (!program) {
        return next(new AppError('Invalid program', 400));
      }
      
      // Check access permissions
      if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
        return next(new AppError('Access denied to this program', 403));
      }
    }

    if (relatedCourseId) {
      const course = await Course.findById(relatedCourseId).populate('program');
      if (!course) {
        return next(new AppError('Invalid course', 400));
      }
      
      // Check access permissions
      if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
        return next(new AppError('Access denied to this course', 403));
      }
    }

    if (relatedDepartmentId) {
      const department = await Department.findById(relatedDepartmentId);
      if (!department) {
        return next(new AppError('Invalid department', 400));
      }
      
      // Check access permissions
      if (req.user.role !== 'ADMIN' && relatedDepartmentId !== req.user.deptId.toString()) {
        return next(new AppError('Access denied to this department', 403));
      }
    }

    const evidenceData = {
      title,
      description,
      criterion,
      subCriterion,
      type,
      link,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      keywords: keywords ? keywords.split(',').map(keyword => keyword.trim()) : [],
      relatedCourseId,
      relatedProgramId,
      relatedDepartmentId,
      academicYear,
      semester: semester ? parseInt(semester) : undefined,
      source,
      dateOfEvidence: dateOfEvidence ? new Date(dateOfEvidence) : undefined,
      validityPeriod,
      accessLevel: accessLevel || 'Internal',
      authorizedRoles: authorizedRoles || [],
      createdBy: req.user._id,
    };

    // Handle file upload
    if (req.file) {
      evidenceData.filePath = req.file.filename;
      evidenceData.fileName = req.file.originalname;
      evidenceData.fileSize = req.file.size;
      evidenceData.mimeType = req.file.mimetype;
    }

    const evidence = new Evidence(evidenceData);
    await evidence.save();

    const populatedEvidence = await Evidence.findById(evidence._id)
      .populate('relatedCourse', 'title code')
      .populate('relatedProgram', 'name code')
      .populate('relatedDepartment', 'name code')
      .populate('createdBy', 'name email');

    return sendCreated(res, 'Evidence created successfully', populatedEvidence);
  } catch (error) {
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    return next(error);
  }
};

// Update evidence
const updateEvidence = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const evidence = await Evidence.findById(id);
    if (!evidence) {
      return sendNotFound(res, 'Evidence not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN') {
      let hasAccess = false;

      if (evidence.relatedProgramId) {
        const program = await Program.findById(evidence.relatedProgramId);
        hasAccess = program && program.deptId.toString() === req.user.deptId.toString();
      } else if (evidence.relatedDepartmentId) {
        hasAccess = evidence.relatedDepartmentId.toString() === req.user.deptId.toString();
      } else if (evidence.createdBy.toString() === req.user._id.toString()) {
        hasAccess = true; // Creator can edit
      }

      if (!hasAccess) {
        return next(new AppError('Access denied', 403));
      }
    }

    // Process tags and keywords
    if (updateData.tags && typeof updateData.tags === 'string') {
      updateData.tags = updateData.tags.split(',').map(tag => tag.trim());
    }
    if (updateData.keywords && typeof updateData.keywords === 'string') {
      updateData.keywords = updateData.keywords.split(',').map(keyword => keyword.trim());
    }

    // Add updatedBy field
    updateData.updatedBy = req.user._id;

    const updatedEvidence = await Evidence.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('relatedCourse', 'title code')
     .populate('relatedProgram', 'name code')
     .populate('relatedDepartment', 'name code')
     .populate('createdBy', 'name email');

    return sendSuccess(res, 'Evidence updated successfully', updatedEvidence);
  } catch (error) {
    return next(error);
  }
};

// Verify evidence
const verifyEvidence = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, comments } = req.body;

    const evidence = await Evidence.findById(id);
    if (!evidence) {
      return sendNotFound(res, 'Evidence not found');
    }

    // Only Admin and Dept Head can verify evidence
    if (!['ADMIN', 'DEPT_HEAD'].includes(req.user.role)) {
      return next(new AppError('Insufficient permissions to verify evidence', 403));
    }

    // Check department access for Dept Head
    if (req.user.role === 'DEPT_HEAD') {
      let hasAccess = false;

      if (evidence.relatedProgramId) {
        const program = await Program.findById(evidence.relatedProgramId);
        hasAccess = program && program.deptId.toString() === req.user.deptId.toString();
      } else if (evidence.relatedDepartmentId) {
        hasAccess = evidence.relatedDepartmentId.toString() === req.user.deptId.toString();
      }

      if (!hasAccess) {
        return next(new AppError('Access denied', 403));
      }
    }

    await evidence.verify(req.user._id, status, comments);

    return sendSuccess(res, 'Evidence verification updated successfully', {
      evidenceId: evidence._id,
      verificationStatus: evidence.verificationStatus,
      verifiedBy: req.user.name,
      verifiedAt: evidence.verifiedAt,
      comments: evidence.verificationComments
    });
  } catch (error) {
    return next(error);
  }
};

// Delete evidence (soft delete)
const deleteEvidence = async (req, res, next) => {
  try {
    const { id } = req.params;

    const evidence = await Evidence.findById(id);
    if (!evidence) {
      return sendNotFound(res, 'Evidence not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN') {
      let hasAccess = false;

      if (evidence.relatedProgramId) {
        const program = await Program.findById(evidence.relatedProgramId);
        hasAccess = program && program.deptId.toString() === req.user.deptId.toString();
      } else if (evidence.relatedDepartmentId) {
        hasAccess = evidence.relatedDepartmentId.toString() === req.user.deptId.toString();
      } else if (evidence.createdBy.toString() === req.user._id.toString()) {
        hasAccess = true; // Creator can delete
      }

      if (!hasAccess) {
        return next(new AppError('Access denied', 403));
      }
    }

    evidence.isActive = false;
    evidence.updatedBy = req.user._id;
    await evidence.save();

    return sendSuccess(res, 'Evidence deleted successfully');
  } catch (error) {
    return next(error);
  }
};

// Download evidence file
const downloadEvidence = async (req, res, next) => {
  try {
    const { id } = req.params;

    const evidence = await Evidence.findById(id);
    if (!evidence) {
      return sendNotFound(res, 'Evidence not found');
    }

    if (!evidence.filePath) {
      return next(new AppError('No file associated with this evidence', 400));
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN') {
      let hasAccess = false;

      if (evidence.relatedProgramId) {
        const program = await Program.findById(evidence.relatedProgramId);
        hasAccess = program && program.deptId.toString() === req.user.deptId.toString();
      } else if (evidence.relatedDepartmentId) {
        hasAccess = evidence.relatedDepartmentId.toString() === req.user.deptId.toString();
      } else {
        hasAccess = true; // General evidence accessible to all
      }

      if (!hasAccess) {
        return next(new AppError('Access denied', 403));
      }
    }

    const filePath = path.join(__dirname, '../../uploads/evidence', evidence.filePath);
    
    if (!fs.existsSync(filePath)) {
      return next(new AppError('File not found', 404));
    }

    // Increment download count
    await evidence.incrementDownloadCount();

    res.download(filePath, evidence.fileName);
  } catch (error) {
    return next(error);
  }
};

// Get evidence by criterion
const getEvidenceByCriterion = async (req, res, next) => {
  try {
    const { criterion } = req.params;
    const { programId } = req.query;

    // Validate criterion
    const validCriteria = ['VisionMission', 'ProgramOutcomes', 'Curriculum', 'TeachingLearning', 'Infra', 'StudentPerformance', 'Faculty', 'Finance', 'ContinuousImprovement'];
    if (!validCriteria.includes(criterion)) {
      return next(new AppError('Invalid NBA criterion', 400));
    }

    // Check access permissions for program
    if (programId && req.user.role !== 'ADMIN') {
      const program = await Program.findById(programId);
      if (!program || program.deptId.toString() !== req.user.deptId.toString()) {
        return next(new AppError('Access denied', 403));
      }
    }

    const evidence = await Evidence.getByCriterion(criterion, programId);

    return sendSuccess(res, `Evidence for ${criterion} retrieved successfully`, evidence);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getEvidence,
  getEvidenceById,
  createEvidence,
  updateEvidence,
  verifyEvidence,
  deleteEvidence,
  downloadEvidence,
  getEvidenceByCriterion,
  upload,
  createEvidenceValidation,
  handleValidationErrors,
};
