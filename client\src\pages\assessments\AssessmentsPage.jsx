import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { assessmentsAPI, coursesAPI } from '../../services/api';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { AssessmentCard } from '../../components/assessments/AssessmentCard';
import { AssessmentModal } from '../../components/assessments/AssessmentModal';
import { QuestionMappingModal } from '../../components/assessments/QuestionMappingModal';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ClipboardDocumentListIcon,
  AcademicCapIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

export const AssessmentsPage = () => {
  const { user } = useAuthStore();
  const [assessments, setAssessments] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAssessments, setTotalAssessments] = useState(0);

  // Modal states
  const [showAssessmentModal, setShowAssessmentModal] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'

  // Assessment types
  const assessmentTypes = [
    'T1', 'T2', 'EndSem', 'Quiz', 'Assignment', 'Lab', 'Project', 'Presentation', 'Viva'
  ];

  const assessmentStatuses = [
    'Draft', 'Published', 'Ongoing', 'Completed', 'Cancelled'
  ];

  // Fetch assessments with filters
  const fetchAssessments = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 12,
        facultyId: user._id, // Only show assessments for courses assigned to this faculty
        ...(searchTerm && { search: searchTerm }),
        ...(selectedCourse && { courseId: selectedCourse }),
        ...(selectedType && { type: selectedType }),
        ...(selectedStatus && { status: selectedStatus }),
      };

      const response = await assessmentsAPI.getAssessments(params);
      setAssessments(response.data.data || []);
      setTotalPages(response.data.meta?.pagination?.totalPages || 1);
      setTotalAssessments(response.data.meta?.pagination?.totalItems || 0);
    } catch (error) {
      console.error('Error fetching assessments:', error);
      toast.error('Failed to fetch assessments');
    } finally {
      setLoading(false);
    }
  };

  // Fetch courses for filter dropdown
  const fetchCourses = async () => {
    try {
      const response = await coursesAPI.getCourses({
        facultyId: user._id,
        limit: 100
      });
      setCourses(response.data.data || []);
    } catch (error) {
      console.error('Error fetching courses:', error);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    fetchAssessments();
  }, [currentPage, searchTerm, selectedCourse, selectedType, selectedStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle assessment creation/editing
  const handleAssessmentSubmit = async (assessmentData) => {
    try {
      if (modalMode === 'create') {
        await assessmentsAPI.createAssessment(assessmentData);
        toast.success('Assessment created successfully');
      } else {
        await assessmentsAPI.updateAssessment(selectedAssessment._id, assessmentData);
        toast.success('Assessment updated successfully');
      }
      setShowAssessmentModal(false);
      setSelectedAssessment(null);
      fetchAssessments();
    } catch (error) {
      console.error('Error saving assessment:', error);
      toast.error(error.response?.data?.message || 'Failed to save assessment');
    }
  };

  // Handle question mapping
  const handleQuestionMappingSubmit = async (questions, coTags) => {
    try {
      await assessmentsAPI.updateAssessment(selectedAssessment._id, {
        questions,
        coTags,
      });
      toast.success('Question mapping updated successfully');
      setShowQuestionModal(false);
      fetchAssessments();
    } catch (error) {
      console.error('Error updating question mapping:', error);
      toast.error(error.response?.data?.message || 'Failed to update question mapping');
    }
  };

  // Handle assessment actions
  const handleEditAssessment = (assessment) => {
    setSelectedAssessment(assessment);
    setModalMode('edit');
    setShowAssessmentModal(true);
  };

  const handleManageQuestions = (assessment) => {
    setSelectedAssessment(assessment);
    setShowQuestionModal(true);
  };

  const handleCreateAssessment = () => {
    setSelectedAssessment(null);
    setModalMode('create');
    setShowAssessmentModal(true);
  };

  const handleStatusChange = async (assessmentId, newStatus) => {
    try {
      await assessmentsAPI.updateAssessmentStatus(assessmentId, { status: newStatus });
      toast.success('Assessment status updated successfully');
      fetchAssessments();
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error.response?.data?.message || 'Failed to update status');
    }
  };

  const handleDeleteAssessment = async (assessmentId) => {
    if (window.confirm('Are you sure you want to delete this assessment?')) {
      try {
        await assessmentsAPI.deleteAssessment(assessmentId);
        toast.success('Assessment deleted successfully');
        fetchAssessments();
      } catch (error) {
        console.error('Error deleting assessment:', error);
        toast.error(error.response?.data?.message || 'Failed to delete assessment');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Assessments</h1>
          <p className="mt-2 text-sm text-gray-700">
            Create and manage assessments with CO-wise question mapping.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            type="button"
            onClick={handleCreateAssessment}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Create Assessment
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search assessments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          {/* Course Filter */}
          <div>
            <select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Courses</option>
              {courses.map((course) => (
                <option key={course._id} value={course._id}>
                  {course.code} - {course.title}
                </option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Types</option>
              {assessmentTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              {assessmentStatuses.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>

          {/* Clear Filters */}
          <div>
            <button
              type="button"
              onClick={() => {
                setSearchTerm('');
                setSelectedCourse('');
                setSelectedType('');
                setSelectedStatus('');
                setCurrentPage(1);
              }}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FunnelIcon className="-ml-1 mr-2 h-4 w-4" aria-hidden="true" />
              Clear
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClipboardDocumentListIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Assessments</dt>
                  <dd className="text-lg font-medium text-gray-900">{totalAssessments}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Upcoming</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {assessments.filter(a => new Date(a.date) > new Date() && a.status !== 'Completed').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AcademicCapIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {assessments.filter(a => a.status === 'Completed').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClipboardDocumentListIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Draft</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {assessments.filter(a => a.status === 'Draft').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assessments Grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      ) : assessments.length > 0 ? (
        <>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {assessments.map((assessment) => (
              <AssessmentCard
                key={assessment._id}
                assessment={assessment}
                onEdit={handleEditAssessment}
                onManageQuestions={handleManageQuestions}
                onStatusChange={handleStatusChange}
                onDelete={handleDeleteAssessment}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{(currentPage - 1) * 12 + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * 12, totalAssessments)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{totalAssessments}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === page
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No assessments found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || selectedCourse || selectedType || selectedStatus
              ? 'Try adjusting your search criteria.'
              : 'Get started by creating a new assessment.'}
          </p>
          {!searchTerm && !selectedCourse && !selectedType && !selectedStatus && (
            <div className="mt-6">
              <button
                type="button"
                onClick={handleCreateAssessment}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Create Assessment
              </button>
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      {showAssessmentModal && (
        <AssessmentModal
          isOpen={showAssessmentModal}
          onClose={() => {
            setShowAssessmentModal(false);
            setSelectedAssessment(null);
          }}
          onSubmit={handleAssessmentSubmit}
          assessment={selectedAssessment}
          mode={modalMode}
          courses={courses}
        />
      )}

      {showQuestionModal && selectedAssessment && (
        <QuestionMappingModal
          isOpen={showQuestionModal}
          onClose={() => {
            setShowQuestionModal(false);
            setSelectedAssessment(null);
          }}
          onSubmit={handleQuestionMappingSubmit}
          assessment={selectedAssessment}
        />
      )}
    </div>
  );
};
