const mongoose = require('mongoose');

const AssessmentSchema = new mongoose.Schema({
  courseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, 'Course is required'],
  },
  title: {
    type: String,
    required: [true, 'Assessment title is required'],
    trim: true,
    maxlength: [200, 'Assessment title cannot exceed 200 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Assessment description cannot exceed 1000 characters'],
  },
  type: {
    type: String,
    enum: {
      values: ['T1', 'T2', 'EndSem', 'Quiz', 'Assignment', 'Lab', 'Project', 'Presentation', 'Viva'],
      message: 'Assessment type must be one of: T1, T2, EndSem, Quiz, Assignment, Lab, Project, Presentation, Viva',
    },
    required: [true, 'Assessment type is required'],
  },
  maxMarks: {
    type: Number,
    required: [true, 'Maximum marks is required'],
    min: [1, 'Maximum marks must be at least 1'],
    max: [1000, 'Maximum marks cannot exceed 1000'],
  },
  weightage: {
    type: Number,
    required: [true, 'Assessment weightage is required'],
    min: [0, 'Weightage cannot be negative'],
    max: [100, 'Weightage cannot exceed 100%'],
  },
  // Question-wise CO mapping with weights
  coTags: [{
    coIndex: {
      type: Number,
      required: true,
      min: [1, 'CO index must be at least 1'],
    },
    weight: {
      type: Number,
      required: true,
      min: [0, 'Weight cannot be negative'],
      max: [1, 'Weight cannot exceed 1'],
    },
    marks: {
      type: Number,
      required: true,
      min: [0, 'Marks cannot be negative'],
    },
  }],
  // Question structure for detailed analysis
  questions: [{
    questionNumber: {
      type: Number,
      required: true,
    },
    marks: {
      type: Number,
      required: true,
      min: [0, 'Question marks cannot be negative'],
    },
    coIndex: {
      type: Number,
      required: true,
      min: [1, 'CO index must be at least 1'],
    },
    bloomsLevel: {
      type: String,
      enum: ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'],
      required: true,
    },
    difficulty: {
      type: String,
      enum: ['Easy', 'Medium', 'Hard'],
      default: 'Medium',
    },
    topic: {
      type: String,
      trim: true,
    },
  }],
  date: {
    type: Date,
    required: [true, 'Assessment date is required'],
  },
  duration: {
    type: Number, // Duration in minutes
    required: [true, 'Assessment duration is required'],
    min: [1, 'Duration must be at least 1 minute'],
    max: [600, 'Duration cannot exceed 600 minutes (10 hours)'],
  },
  instructions: {
    type: String,
    trim: true,
    maxlength: [2000, 'Instructions cannot exceed 2000 characters'],
  },
  // Assessment configuration
  configuration: {
    isOnline: {
      type: Boolean,
      default: false,
    },
    allowCalculator: {
      type: Boolean,
      default: false,
    },
    openBook: {
      type: Boolean,
      default: false,
    },
    randomizeQuestions: {
      type: Boolean,
      default: false,
    },
    showResultsImmediately: {
      type: Boolean,
      default: false,
    },
  },
  // Grading scheme
  gradingScheme: {
    passingMarks: {
      type: Number,
      default: 40,
      min: [0, 'Passing marks cannot be negative'],
      max: [100, 'Passing marks cannot exceed 100%'],
    },
    gradeBoundaries: [{
      grade: String,
      minPercentage: Number,
      maxPercentage: Number,
    }],
  },
  // Assessment status
  status: {
    type: String,
    enum: ['Draft', 'Published', 'Conducted', 'Evaluated', 'Completed'],
    default: 'Draft',
  },
  // File attachments
  attachments: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    mimeType: String,
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  // Statistics (computed after evaluation)
  statistics: {
    totalStudents: {
      type: Number,
      default: 0,
    },
    studentsAppeared: {
      type: Number,
      default: 0,
    },
    averageMarks: {
      type: Number,
      default: 0,
    },
    highestMarks: {
      type: Number,
      default: 0,
    },
    lowestMarks: {
      type: Number,
      default: 0,
    },
    passPercentage: {
      type: Number,
      default: 0,
    },
    standardDeviation: {
      type: Number,
      default: 0,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  evaluatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  evaluatedAt: {
    type: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
AssessmentSchema.index({ courseId: 1, type: 1 });
AssessmentSchema.index({ date: 1 });
AssessmentSchema.index({ status: 1 });
AssessmentSchema.index({ createdBy: 1 });
AssessmentSchema.index({ isActive: 1 });

// Virtual for course details
AssessmentSchema.virtual('course', {
  ref: 'Course',
  localField: 'courseId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for results count
AssessmentSchema.virtual('resultsCount', {
  ref: 'Result',
  localField: '_id',
  foreignField: 'assessmentId',
  count: true,
});

// Pre-save middleware to validate CO tags total weight
AssessmentSchema.pre('save', function(next) {
  const totalWeight = this.coTags.reduce((sum, tag) => sum + tag.weight, 0);
  if (Math.abs(totalWeight - 1) > 0.01) { // Allow small floating point errors
    return next(new Error('Total CO weights must sum to 1.0'));
  }

  // Validate that question marks sum to maxMarks
  const totalQuestionMarks = this.questions.reduce((sum, q) => sum + q.marks, 0);
  if (totalQuestionMarks !== this.maxMarks) {
    return next(new Error('Sum of question marks must equal maximum marks'));
  }

  next();
});

// Static method to get assessments by course
AssessmentSchema.statics.getByCourse = function(courseId, status = null) {
  const filter = { courseId, isActive: true };
  if (status) filter.status = status;

  return this.find(filter)
    .populate('course', 'title code')
    .sort({ date: -1 });
};

// Static method to get assessments by faculty
AssessmentSchema.statics.getByFaculty = function(facultyId, year = null) {
  const Course = mongoose.model('Course');

  return Course.find({
    $or: [
      { facultyIds: facultyId },
      { coordinator: facultyId }
    ],
    ...(year && { year }),
    isActive: true
  }).then(courses => {
    const courseIds = courses.map(course => course._id);
    return this.find({ courseId: { $in: courseIds }, isActive: true })
      .populate('course', 'title code semester year')
      .sort({ date: -1 });
  });
};

// Instance method to calculate CO-wise statistics
AssessmentSchema.methods.calculateCOStatistics = async function() {
  const Result = mongoose.model('Result');

  const results = await Result.find({ assessmentId: this._id });
  const coStats = {};

  // Initialize CO statistics
  this.coTags.forEach(tag => {
    coStats[`CO${tag.coIndex}`] = {
      totalMarks: tag.marks,
      weight: tag.weight,
      students: [],
      average: 0,
      attainment: 0,
    };
  });

  // Calculate statistics for each CO
  results.forEach(result => {
    this.questions.forEach(question => {
      const coKey = `CO${question.coIndex}`;
      if (coStats[coKey]) {
        const questionResult = result.questionWise.find(q => q.q === question.questionNumber);
        if (questionResult) {
          coStats[coKey].students.push({
            studentRoll: result.studentRoll,
            marks: questionResult.marks,
            percentage: (questionResult.marks / question.marks) * 100,
          });
        }
      }
    });
  });

  // Calculate averages and attainment
  Object.keys(coStats).forEach(coKey => {
    const co = coStats[coKey];
    if (co.students.length > 0) {
      co.average = co.students.reduce((sum, s) => sum + s.percentage, 0) / co.students.length;
      co.attainment = co.average; // Can be modified based on target criteria
    }
  });

  return coStats;
};

// Instance method to update statistics
AssessmentSchema.methods.updateStatistics = async function() {
  const Result = mongoose.model('Result');

  const results = await Result.find({ assessmentId: this._id });

  if (results.length === 0) {
    return this;
  }

  const marks = results.map(r => r.obtained);
  const passingMarks = (this.gradingScheme.passingMarks / 100) * this.maxMarks;
  const passedStudents = marks.filter(mark => mark >= passingMarks).length;

  // Calculate statistics
  const totalMarks = marks.reduce((sum, mark) => sum + mark, 0);
  const average = totalMarks / marks.length;
  const highest = Math.max(...marks);
  const lowest = Math.min(...marks);
  const passPercentage = (passedStudents / marks.length) * 100;

  // Calculate standard deviation
  const variance = marks.reduce((sum, mark) => sum + Math.pow(mark - average, 2), 0) / marks.length;
  const standardDeviation = Math.sqrt(variance);

  this.statistics = {
    totalStudents: results.length,
    studentsAppeared: results.length,
    averageMarks: Math.round(average * 100) / 100,
    highestMarks: highest,
    lowestMarks: lowest,
    passPercentage: Math.round(passPercentage * 100) / 100,
    standardDeviation: Math.round(standardDeviation * 100) / 100,
  };

  return this.save();
};

module.exports = mongoose.model('Assessment', AssessmentSchema);
