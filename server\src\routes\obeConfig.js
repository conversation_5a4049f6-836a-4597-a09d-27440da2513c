const express = require('express');
const {
  getOBEConfigByProgram,
  createOrUpdateOBEConfig,
  getAllOBEConfigs,
  updateAttainmentBands,
  updateAssessmentWeights,
  updateTargets,
  resetToDefault,
  deleteOBEConfig,
  createOBEConfigValidation,
  handleValidationErrors,
} = require('../controllers/obeConfigController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all OBE configurations (Admin and Dept Head only)
router.get('/', authorize('ADMIN', 'DEPT_HEAD'), getAllOBEConfigs);

// Get OBE config by program
router.get('/program/:programId', getOBEConfigByProgram);

// Create or update OBE config (Admin and Dept Head only)
router.post('/program/:programId', authorize('ADMIN', 'DEPT_HEAD'), createOBEConfigValidation, handleValidationErrors, createOrUpdateOBEConfig);

// Update specific sections
router.put('/program/:programId/attainment-bands', authorize('ADMIN', 'DEPT_HEAD'), updateAttainmentBands);
router.put('/program/:programId/assessment-weights', authorize('ADMIN', 'DEPT_HEAD'), updateAssessmentWeights);
router.put('/program/:programId/targets', authorize('ADMIN', 'DEPT_HEAD'), updateTargets);

// Reset to default configuration (Admin and Dept Head only)
router.post('/program/:programId/reset', authorize('ADMIN', 'DEPT_HEAD'), resetToDefault);

// Delete OBE config (Admin only)
router.delete('/program/:programId', authorize('ADMIN'), deleteOBEConfig);

module.exports = router;
