# NBA Accreditation Readiness & OBE Attainment Suite

A comprehensive MERN stack web application that helps educational institutes measure and improve their NBA (National Board of Accreditation) accreditation readiness using Outcome Based Education (OBE) principles.

## 🎯 Features

- **Complete OBE Management**: Course Outcomes (COs), Program Outcomes (POs), CO-PO mapping, and attainment calculation
- **NBA Readiness Tracking**: Real-time monitoring against NBA criteria with configurable thresholds
- **Assessment Management**: Comprehensive assessment creation, marks upload, and result analysis
- **Evidence Repository**: Document management with NBA criteria mapping
- **SAR Generation**: Automated Self-Assessment Report generation in PDF format
- **Role-based Access**: Admin, Department Head, Faculty, and Auditor roles with appropriate permissions
- **Analytics Dashboard**: Interactive charts and visualizations for attainment tracking

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React + Vite, Zustand (state management), React Router, shadcn/ui, Recharts
- **Backend**: Node.js + Express, MongoDB + Mongoose, JWT authentication
- **Deployment**: Vercel (frontend), Railway/Render (backend), MongoDB Atlas

### Project Structure
```
NBA-BTP/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── stores/        # Zustand stores
│   │   ├── services/      # API services
│   │   ├── utils/         # Utility functions
│   │   └── types/         # TypeScript types
│   ├── public/
│   └── package.json
├── server/                # Node.js backend
│   ├── src/
│   │   ├── controllers/   # Route controllers
│   │   ├── models/        # Mongoose models
│   │   ├── middleware/    # Custom middleware
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic
│   │   ├── utils/         # Utility functions
│   │   └── config/        # Configuration files
│   ├── uploads/           # File uploads
│   └── package.json
├── docs/                  # Documentation
└── package.json          # Root package.json
```

## 🚀 Quick Start

### Prerequisites
- Node.js >= 18.0.0
- npm >= 9.0.0
- MongoDB Atlas account (or local MongoDB)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd NBA-BTP
```

2. Install dependencies for all packages:
```bash
npm run install:all
```

3. Set up environment variables:
```bash
# Copy example env files
cp server/.env.example server/.env
cp client/.env.example client/.env
```

4. Configure your environment variables in the `.env` files

5. Start the development servers:
```bash
npm run dev
```

This will start:
- Backend server on http://localhost:3000
- Frontend development server on http://localhost:5173

## 📊 Core Modules

### 1. User Management & Authentication
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- User profile management

### 2. Program & Course Management
- Department and program configuration
- Course creation with CO definitions
- CO-PO/PSO mapping with level weights

### 3. Assessment & Results
- Assessment creation with CO mapping
- Bulk marks upload (CSV support)
- Question-wise result analysis

### 4. OBE Calculation Engine
- Configurable direct and indirect attainment formulas
- CO attainment calculation
- PO/PSO aggregation with mapping weights

### 5. NBA Readiness Tracking
- Configurable criteria weights
- Real-time readiness percentage calculation
- Evidence mapping to criteria

### 6. Reporting & Analytics
- Interactive dashboards with charts
- Trend analysis and comparisons
- Automated SAR PDF generation

## 🔧 Configuration

### OBE Configuration
The system supports configurable OBE parameters:
- Direct vs Indirect assessment weights
- CO-PO mapping level weights (L1: 1, L2: 2, L3: 3)
- Attainment threshold bands
- Aggregation strategies

### NBA Criteria Weights
Default NBA criteria weights (customizable):
- Faculty: 25%
- Curriculum/Delivery: 20%
- Student Performance: 25%
- Infrastructure: 15%
- Continuous Improvement: 15%

## 📝 API Documentation

Base URL: `http://localhost:3000/api`

### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration (Admin only)
- `POST /auth/refresh` - Refresh access token

### Programs & Courses
- `GET /programs` - List programs
- `POST /programs` - Create program
- `GET /courses` - List courses
- `POST /courses` - Create course with COs

### Assessments & Results
- `POST /assessments` - Create assessment
- `POST /results/bulk` - Bulk upload results
- `GET /attainment/course/:id` - Get course attainment

## 🧪 Testing

Run tests:
```bash
npm test
```

## 📦 Deployment

### Frontend (Vercel)
```bash
cd client
npm run build
# Deploy to Vercel
```

### Backend (Railway/Render)
```bash
cd server
# Configure environment variables
# Deploy to Railway or Render
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions, please open an issue in the GitHub repository.
