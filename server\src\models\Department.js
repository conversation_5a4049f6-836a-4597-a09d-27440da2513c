const mongoose = require('mongoose');

const DepartmentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Department name is required'],
    trim: true,
    maxlength: [100, 'Department name cannot exceed 100 characters'],
  },
  code: {
    type: String,
    required: [true, 'Department code is required'],
    unique: true,
    uppercase: true,
    trim: true,
    maxlength: [10, 'Department code cannot exceed 10 characters'],
    match: [/^[A-Z]{2,10}$/, 'Department code must contain only uppercase letters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
  },
  establishedYear: {
    type: Number,
    min: [1900, 'Established year must be after 1900'],
    max: [new Date().getFullYear(), 'Established year cannot be in the future'],
  },
  headOfDepartment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  contactInfo: {
    email: {
      type: String,
      lowercase: true,
      trim: true,
      match: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please provide a valid email address'],
    },
    phone: {
      type: String,
      trim: true,
    },
    address: {
      type: String,
      trim: true,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes (code index is automatically created by unique: true)
DepartmentSchema.index({ isActive: 1 });

// Virtual for faculty count
DepartmentSchema.virtual('facultyCount', {
  ref: 'User',
  localField: '_id',
  foreignField: 'deptId',
  count: true,
  match: { isActive: true },
});

// Virtual for programs count
DepartmentSchema.virtual('programsCount', {
  ref: 'Program',
  localField: '_id',
  foreignField: 'deptId',
  count: true,
});

// Static method to find by code
DepartmentSchema.statics.findByCode = function(code) {
  return this.findOne({ code: code.toUpperCase() });
};

// Static method to get active departments
DepartmentSchema.statics.getActive = function() {
  return this.find({ isActive: true }).populate('headOfDepartment', 'name email');
};

// Instance method to get department statistics
DepartmentSchema.methods.getStatistics = async function() {
  const User = mongoose.model('User');
  const Program = mongoose.model('Program');
  
  const [facultyCount, programsCount] = await Promise.all([
    User.countDocuments({ deptId: this._id, isActive: true }),
    Program.countDocuments({ deptId: this._id }),
  ]);

  return {
    facultyCount,
    programsCount,
    establishedYear: this.establishedYear,
    isActive: this.isActive,
  };
};

module.exports = mongoose.model('Department', DepartmentSchema);
