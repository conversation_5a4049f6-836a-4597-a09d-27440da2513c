import { classNames } from '../../utils/helpers';

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12',
};

export const LoadingSpinner = ({ size = 'md', className = '', color = 'blue' }) => {
  return (
    <div className={classNames('flex items-center justify-center', className)}>
      <div
        className={classNames(
          'animate-spin rounded-full border-2 border-gray-300',
          sizeClasses[size],
          color === 'blue' && 'border-t-blue-600',
          color === 'white' && 'border-t-white',
          color === 'gray' && 'border-t-gray-600'
        )}
      />
    </div>
  );
};
