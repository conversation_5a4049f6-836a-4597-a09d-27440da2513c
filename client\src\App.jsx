import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { useAuthStore } from './stores/authStore';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { Layout } from './components/layout/Layout';
import { LoadingSpinner } from './components/ui/LoadingSpinner';

// Auth Pages
import { LoginPage } from './pages/auth/LoginPage';

// Dashboard Pages
import { AdminDashboard } from './pages/dashboard/AdminDashboard';
import { DeptHeadDashboard } from './pages/dashboard/DeptHeadDashboard';
import { FacultyDashboard } from './pages/dashboard/FacultyDashboard';
import { AuditorDashboard } from './pages/dashboard/AuditorDashboard';

// Management Pages
import { UsersPage } from './pages/users/UsersPage';
import { DepartmentsPage } from './pages/departments/DepartmentsPage';
import { ProgramsPage } from './pages/programs/ProgramsPage';
import { CoursesPage } from './pages/courses/CoursesPage';
import { AssessmentsPage } from './pages/assessments/AssessmentsPage';
import { ResultsPage } from './pages/results/ResultsPage';

// OBE Pages
import { AttainmentPage } from './pages/obe/AttainmentPage';
import { OBEConfigPage } from './pages/obe/OBEConfigPage';

// NBA Pages
import { ReadinessPage } from './pages/nba/ReadinessPage';
import { EvidencePage } from './pages/nba/EvidencePage';
import { ReportsPage } from './pages/reports/ReportsPage';

// Profile Page
import { ProfilePage } from './pages/profile/ProfilePage';

function App() {
  const { user, isAuthenticated, isLoading } = useAuthStore();

  // Initialize authentication on app mount
  useEffect(() => {
    console.log('App: Initializing authentication check...');
    const currentState = useAuthStore.getState();
    console.log('App: Initial auth state:', {
      isAuthenticated: currentState.isAuthenticated,
      isLoading: currentState.isLoading,
      user: currentState.user?.name || 'No user'
    });

    // Clear any invalid state where we're authenticated but have no user
    if (currentState.isAuthenticated && !currentState.user) {
      console.log('App: Invalid state detected - authenticated but no user, clearing...');
      useAuthStore.getState().logout();
      return;
    }

    useAuthStore.getState().checkAuth();
  }, []); // Empty dependency array - only run once on mount

  // Debug logging
  useEffect(() => {
    console.log('App: Auth state changed:', { isAuthenticated, isLoading, user: user?.name });
  }, [isAuthenticated, isLoading, user]);

  // helper fn to render dashboard based on role
  const getDashboardComponent = () => {
    if (!user) return <Navigate to="/login" replace />;

    switch (user.role) {
      case 'ADMIN':
        return <AdminDashboard />;
      case 'DEPT_HEAD':
        return <DeptHeadDashboard />;
      case 'FACULTY':
        return <FacultyDashboard />;
      case 'AUDITOR':
        return <AuditorDashboard />;
      default:
        return <Navigate to="/login" replace />;
    }
  };

  // Show loading screen during initial authentication check
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="App">
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              theme: {
                primary: '#4aed88',
              },
            },
            error: {
              duration: 5000,
              theme: {
                primary: '#f56565',
              },
            },
          }}
        />

        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
            }
          />

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={getDashboardComponent()} />

            {/* User Management */}
            <Route
              path="users"
              element={
                <ProtectedRoute requiredRoles={['ADMIN', 'DEPT_HEAD']}>
                  <UsersPage />
                </ProtectedRoute>
              }
            />

            {/* Department Management */}
            <Route
              path="departments"
              element={
                <ProtectedRoute requiredRoles={['ADMIN']}>
                  <DepartmentsPage />
                </ProtectedRoute>
              }
            />

            {/* Program Management */}
            <Route
              path="programs"
              element={
                <ProtectedRoute requiredRoles={['ADMIN', 'DEPT_HEAD']}>
                  <ProgramsPage />
                </ProtectedRoute>
              }
            />

            {/* Course Management */}
            <Route path="courses" element={<CoursesPage />} />

            {/* Assessment Management */}
            <Route path="assessments" element={<AssessmentsPage />} />

            {/* Results Management */}
            <Route path="results" element={<ResultsPage />} />

            {/* OBE Management */}
            <Route path="obe/attainment" element={<AttainmentPage />} />
            <Route
              path="obe/config"
              element={
                <ProtectedRoute requiredRoles={['ADMIN', 'DEPT_HEAD']}>
                  <OBEConfigPage />
                </ProtectedRoute>
              }
            />

            {/* NBA Management */}
            <Route path="nba/readiness" element={<ReadinessPage />} />
            <Route path="nba/evidence" element={<EvidencePage />} />

            {/* Reports */}
            <Route path="reports" element={<ReportsPage />} />

            {/* Profile */}
            <Route path="profile" element={<ProfilePage />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
