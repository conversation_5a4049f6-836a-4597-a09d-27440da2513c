const mongoose = require('mongoose');

const ResultSchema = new mongoose.Schema({
  assessmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Assessment',
    required: [true, 'Assessment is required'],
  },
  studentRoll: {
    type: String,
    required: [true, 'Student roll number is required'],
    trim: true,
    uppercase: true,
    match: [/^\d{4}[A-Z]{2,4}\d{3}$/, 'Invalid roll number format (e.g., 2020CSE001)'],
  },
  studentName: {
    type: String,
    required: [true, 'Student name is required'],
    trim: true,
    maxlength: [100, 'Student name cannot exceed 100 characters'],
  },
  obtained: {
    type: Number,
    required: [true, 'Obtained marks is required'],
    min: [0, 'Obtained marks cannot be negative'],
  },
  // Question-wise marks for detailed analysis
  questionWise: [{
    q: {
      type: Number,
      required: true,
      min: [1, 'Question number must be at least 1'],
    },
    marks: {
      type: Number,
      required: true,
      min: [0, 'Question marks cannot be negative'],
    },
    maxMarks: {
      type: Number,
      required: true,
      min: [0, 'Maximum marks cannot be negative'],
    },
    coIndex: {
      type: Number,
      required: true,
      min: [1, 'CO index must be at least 1'],
    },
  }],
  // Calculated fields
  percentage: {
    type: Number,
    min: [0, 'Percentage cannot be negative'],
    max: [100, 'Percentage cannot exceed 100'],
  },
  grade: {
    type: String,
    trim: true,
    maxlength: [5, 'Grade cannot exceed 5 characters'],
  },
  gradePoints: {
    type: Number,
    min: [0, 'Grade points cannot be negative'],
    max: [10, 'Grade points cannot exceed 10'],
  },
  status: {
    type: String,
    enum: ['Pass', 'Fail', 'Absent', 'Malpractice'],
    default: 'Pass',
  },
  // Additional details
  submissionTime: {
    type: Date,
  },
  evaluationDate: {
    type: Date,
  },
  remarks: {
    type: String,
    trim: true,
    maxlength: [500, 'Remarks cannot exceed 500 characters'],
  },
  // CO-wise performance
  coPerformance: [{
    coIndex: {
      type: Number,
      required: true,
    },
    obtainedMarks: {
      type: Number,
      required: true,
      min: [0, 'Obtained marks cannot be negative'],
    },
    totalMarks: {
      type: Number,
      required: true,
      min: [0, 'Total marks cannot be negative'],
    },
    percentage: {
      type: Number,
      min: [0, 'Percentage cannot be negative'],
      max: [100, 'Percentage cannot exceed 100'],
    },
    attained: {
      type: Boolean,
      default: false,
    },
  }],
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  evaluatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes
ResultSchema.index({ assessmentId: 1, studentRoll: 1 }, { unique: true });
ResultSchema.index({ studentRoll: 1 });
ResultSchema.index({ status: 1 });
ResultSchema.index({ createdBy: 1 });

// Virtual for assessment details
ResultSchema.virtual('assessment', {
  ref: 'Assessment',
  localField: 'assessmentId',
  foreignField: '_id',
  justOne: true,
});

// Pre-save middleware to calculate derived fields
ResultSchema.pre('save', async function(next) {
  try {
    // Get assessment details to calculate percentage and grade
    const assessment = await mongoose.model('Assessment').findById(this.assessmentId);
    if (!assessment) {
      return next(new Error('Assessment not found'));
    }

    // Calculate percentage
    this.percentage = Math.round((this.obtained / assessment.maxMarks) * 100 * 100) / 100;

    // Calculate grade based on assessment's grading scheme
    if (assessment.gradingScheme && assessment.gradingScheme.gradeBoundaries.length > 0) {
      const gradeBoundary = assessment.gradingScheme.gradeBoundaries.find(
        boundary => this.percentage >= boundary.minPercentage && this.percentage <= boundary.maxPercentage
      );
      if (gradeBoundary) {
        this.grade = gradeBoundary.grade;
      }
    }

    // Determine pass/fail status
    const passingPercentage = assessment.gradingScheme?.passingMarks || 40;
    if (this.percentage >= passingPercentage) {
      this.status = 'Pass';
    } else if (this.status !== 'Absent' && this.status !== 'Malpractice') {
      this.status = 'Fail';
    }

    // Calculate CO-wise performance
    if (this.questionWise.length > 0) {
      const coPerformanceMap = new Map();

      this.questionWise.forEach(qw => {
        if (!coPerformanceMap.has(qw.coIndex)) {
          coPerformanceMap.set(qw.coIndex, {
            coIndex: qw.coIndex,
            obtainedMarks: 0,
            totalMarks: 0,
          });
        }
        const coPerf = coPerformanceMap.get(qw.coIndex);
        coPerf.obtainedMarks += qw.marks;
        coPerf.totalMarks += qw.maxMarks;
      });

      this.coPerformance = Array.from(coPerformanceMap.values()).map(co => ({
        ...co,
        percentage: Math.round((co.obtainedMarks / co.totalMarks) * 100 * 100) / 100,
        attained: (co.obtainedMarks / co.totalMarks) >= 0.5, // 50% threshold for attainment
      }));
    }

    next();
  } catch (error) {
    next(error);
  }
});

// Static method to get results by assessment
ResultSchema.statics.getByAssessment = function(assessmentId, status = null) {
  const filter = { assessmentId };
  if (status) filter.status = status;
  
  return this.find(filter)
    .populate('assessment', 'title type maxMarks')
    .sort({ studentRoll: 1 });
};

// Static method to get results by student
ResultSchema.statics.getByStudent = function(studentRoll, courseId = null) {
  const pipeline = [
    { $match: { studentRoll } },
    {
      $lookup: {
        from: 'assessments',
        localField: 'assessmentId',
        foreignField: '_id',
        as: 'assessment'
      }
    },
    { $unwind: '$assessment' },
  ];

  if (courseId) {
    pipeline.push({ $match: { 'assessment.courseId': mongoose.Types.ObjectId(courseId) } });
  }

  pipeline.push(
    {
      $lookup: {
        from: 'courses',
        localField: 'assessment.courseId',
        foreignField: '_id',
        as: 'course'
      }
    },
    { $unwind: '$course' },
    { $sort: { 'assessment.date': -1 } }
  );

  return this.aggregate(pipeline);
};

// Static method to calculate class statistics
ResultSchema.statics.getClassStatistics = async function(assessmentId) {
  const results = await this.find({ assessmentId, status: { $ne: 'Absent' } });
  
  if (results.length === 0) {
    return null;
  }

  const marks = results.map(r => r.obtained);
  const total = marks.reduce((sum, mark) => sum + mark, 0);
  const average = total / marks.length;
  const highest = Math.max(...marks);
  const lowest = Math.min(...marks);
  
  // Calculate standard deviation
  const variance = marks.reduce((sum, mark) => sum + Math.pow(mark - average, 2), 0) / marks.length;
  const standardDeviation = Math.sqrt(variance);
  
  // Calculate pass percentage
  const assessment = await mongoose.model('Assessment').findById(assessmentId);
  const passingMarks = assessment ? (assessment.gradingScheme?.passingMarks || 40) : 40;
  const passingScore = (passingMarks / 100) * assessment.maxMarks;
  const passedStudents = marks.filter(mark => mark >= passingScore).length;
  const passPercentage = (passedStudents / marks.length) * 100;

  return {
    totalStudents: results.length,
    averageMarks: Math.round(average * 100) / 100,
    averagePercentage: Math.round((average / assessment.maxMarks) * 100 * 100) / 100,
    highestMarks: highest,
    lowestMarks: lowest,
    standardDeviation: Math.round(standardDeviation * 100) / 100,
    passPercentage: Math.round(passPercentage * 100) / 100,
    gradeDistribution: await this.getGradeDistribution(assessmentId),
  };
};

// Static method to get grade distribution
ResultSchema.statics.getGradeDistribution = async function(assessmentId) {
  const distribution = await this.aggregate([
    { $match: { assessmentId: mongoose.Types.ObjectId(assessmentId) } },
    { $group: { _id: '$grade', count: { $sum: 1 } } },
    { $sort: { _id: 1 } }
  ]);

  return distribution.reduce((acc, item) => {
    acc[item._id || 'Ungraded'] = item.count;
    return acc;
  }, {});
};

// Instance method to calculate CO attainment for this result
ResultSchema.methods.calculateCOAttainment = function(targetPercentage = 50) {
  return this.coPerformance.map(co => ({
    coIndex: co.coIndex,
    percentage: co.percentage,
    attained: co.percentage >= targetPercentage,
    status: co.percentage >= targetPercentage ? 'Attained' : 'Not Attained',
  }));
};

module.exports = mongoose.model('Result', ResultSchema);
