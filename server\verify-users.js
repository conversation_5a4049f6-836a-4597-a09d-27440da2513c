const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import models
const User = require('./src/models/User');
const Department = require('./src/models/Department');

const verifyUsers = async () => {
  try {
    console.log('🔌 Connecting to MongoDB...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ Connected to MongoDB');

    // Test credentials
    const testCredentials = [
      { email: '<EMAIL>', password: 'admin123', role: 'ADMIN' },
      { email: '<EMAIL>', password: 'dept123', role: 'DEPT_HEAD' },
      { email: '<EMAIL>', password: 'faculty123', role: 'FACULTY' }
    ];

    console.log('\n🔍 Checking users in database...\n');

    for (const cred of testCredentials) {
      console.log(`📧 Checking ${cred.email}:`);
      
      // Find user in database
      const user = await User.findByEmail(cred.email).select('+passwordHash');
      
      if (!user) {
        console.log(`   ❌ User not found in database`);
        continue;
      }
      
      console.log(`   ✅ User found: ${user.name}`);
      console.log(`   🎭 Role: ${user.role}`);
      console.log(`   🔄 Active: ${user.isActive}`);
      console.log(`   🏢 Department ID: ${user.deptId || 'N/A'}`);
      
      // Test password
      const isPasswordValid = await user.comparePassword(cred.password);
      console.log(`   🔑 Password valid: ${isPasswordValid ? '✅ Yes' : '❌ No'}`);
      
      if (!isPasswordValid) {
        console.log(`   🔧 Attempting to reset password...`);
        user.passwordHash = cred.password; // Will be hashed by pre-save middleware
        await user.save();
        console.log(`   ✅ Password reset successfully`);
      }
      
      console.log('');
    }

    console.log('🎉 User verification completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run the verification
verifyUsers();
