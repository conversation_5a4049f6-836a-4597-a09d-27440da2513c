{"name": "nba-obe-server", "version": "1.0.0", "description": "Backend server for NBA Accreditation Readiness & OBE Attainment Suite", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "puppeteer": "^21.6.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["express", "mongodb", "jwt", "obe", "nba", "education"]}