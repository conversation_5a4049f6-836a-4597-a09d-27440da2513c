const mongoose = require('mongoose');
const logger = require('../utils/logger');
const dotenv = require('dotenv');
dotenv.config();

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI;

    if (!mongoURI) {
      logger.error("❌ No MONGODB_URI found in .env file");
      process.exit(1);
    }

    const conn = await mongoose.connect(mongoURI, {
      maxPoolSize: 10,                // maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // timeout after 5s if no server found
      socketTimeoutMS: 45000,         // close sockets after 45s idle
    });

    logger.info(`✅ MongoDB Connected: ${conn.connection.host}`);

    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    console.error("❌ FULL MongoDB Connection Error:", error);
    logger.error(`❌ Error connecting to MongoDB: ${error.message || 'No message provided'}`);
    if (error.reason) {
      logger.error(`Reason: ${error.reason}`);
    }
    logger.warn('Server will continue without database connection');

    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
};

module.exports = connectDB;
