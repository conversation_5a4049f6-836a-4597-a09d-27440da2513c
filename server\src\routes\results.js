const express = require('express');
const {
  getResults,
  getResultById,
  createResult,
  bulkUploadResults,
  upload,
  createResultValidation,
  handleValidationErrors,
} = require('../controllers/resultController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all results (with filtering and pagination)
router.get('/', getResults);

// Get result by ID
router.get('/:id', getResultById);

// Create single result (Admin, Dept Head, and assigned Faculty)
router.post('/', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), createResultValidation, handleValidationErrors, createResult);

// Bulk upload results via CSV (Admin, Dept Head, and assigned Faculty)
router.post('/bulk', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), upload.single('csvFile'), bulkUploadResults);

module.exports = router;
