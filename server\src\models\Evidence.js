const mongoose = require('mongoose');

const EvidenceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Evidence title is required'],
    trim: true,
    maxlength: [200, 'Evidence title cannot exceed 200 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Evidence description cannot exceed 1000 characters'],
  },
  criterion: {
    type: String,
    enum: {
      values: [
        'VisionMission',
        'ProgramOutcomes', 
        'Curriculum',
        'TeachingLearning',
        'Infra',
        'StudentPerformance',
        'Faculty',
        'Finance',
        'ContinuousImprovement'
      ],
      message: 'Invalid NBA criterion',
    },
    required: [true, 'NBA criterion is required'],
  },
  subCriterion: {
    type: String,
    trim: true,
    maxlength: [100, 'Sub-criterion cannot exceed 100 characters'],
  },
  type: {
    type: String,
    enum: {
      values: ['Document', 'Link', 'Image', 'Video', 'Data', 'Report'],
      message: 'Evidence type must be one of: Document, Link, Image, Video, Data, Report',
    },
    required: [true, 'Evidence type is required'],
  },
  // File or link information
  link: {
    type: String,
    trim: true,
    maxlength: [500, 'Link cannot exceed 500 characters'],
  },
  filePath: {
    type: String,
    trim: true,
  },
  fileName: {
    type: String,
    trim: true,
  },
  fileSize: {
    type: Number,
    min: [0, 'File size cannot be negative'],
  },
  mimeType: {
    type: String,
    trim: true,
  },
  // Categorization
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Tag cannot exceed 50 characters'],
  }],
  keywords: [{
    type: String,
    trim: true,
    maxlength: [50, 'Keyword cannot exceed 50 characters'],
  }],
  // Related entities
  relatedCourseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
  },
  relatedProgramId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Program',
  },
  relatedDepartmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
  },
  // Academic context
  academicYear: {
    type: String,
    trim: true,
    match: [/^\d{4}-\d{4}$/, 'Academic year format should be YYYY-YYYY (e.g., 2023-2024)'],
  },
  semester: {
    type: Number,
    min: [1, 'Semester must be at least 1'],
    max: [20, 'Semester cannot exceed 20'],
  },
  // Evidence metadata
  source: {
    type: String,
    trim: true,
    maxlength: [200, 'Source cannot exceed 200 characters'],
  },
  dateOfEvidence: {
    type: Date,
  },
  validityPeriod: {
    startDate: Date,
    endDate: Date,
  },
  // Access control
  accessLevel: {
    type: String,
    enum: {
      values: ['Public', 'Internal', 'Restricted', 'Confidential'],
      message: 'Access level must be one of: Public, Internal, Restricted, Confidential',
    },
    default: 'Internal',
  },
  authorizedRoles: [{
    type: String,
    enum: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'],
  }],
  // Verification status
  verificationStatus: {
    type: String,
    enum: {
      values: ['Pending', 'Verified', 'Rejected', 'Needs Review'],
      message: 'Verification status must be one of: Pending, Verified, Rejected, Needs Review',
    },
    default: 'Pending',
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  verifiedAt: {
    type: Date,
  },
  verificationComments: {
    type: String,
    trim: true,
    maxlength: [500, 'Verification comments cannot exceed 500 characters'],
  },
  // Quality metrics
  qualityScore: {
    type: Number,
    min: [0, 'Quality score cannot be negative'],
    max: [100, 'Quality score cannot exceed 100'],
  },
  relevanceScore: {
    type: Number,
    min: [0, 'Relevance score cannot be negative'],
    max: [100, 'Relevance score cannot exceed 100'],
  },
  // Usage tracking
  viewCount: {
    type: Number,
    default: 0,
    min: [0, 'View count cannot be negative'],
  },
  downloadCount: {
    type: Number,
    default: 0,
    min: [0, 'Download count cannot be negative'],
  },
  lastAccessed: {
    type: Date,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
EvidenceSchema.index({ criterion: 1 });
EvidenceSchema.index({ type: 1 });
EvidenceSchema.index({ tags: 1 });
EvidenceSchema.index({ keywords: 1 });
EvidenceSchema.index({ relatedCourseId: 1 });
EvidenceSchema.index({ relatedProgramId: 1 });
EvidenceSchema.index({ relatedDepartmentId: 1 });
EvidenceSchema.index({ academicYear: 1 });
EvidenceSchema.index({ verificationStatus: 1 });
EvidenceSchema.index({ isActive: 1 });
EvidenceSchema.index({ createdBy: 1 });

// Text index for search
EvidenceSchema.index({
  title: 'text',
  description: 'text',
  tags: 'text',
  keywords: 'text',
  source: 'text',
});

// Virtual for related course details
EvidenceSchema.virtual('relatedCourse', {
  ref: 'Course',
  localField: 'relatedCourseId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for related program details
EvidenceSchema.virtual('relatedProgram', {
  ref: 'Program',
  localField: 'relatedProgramId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for related department details
EvidenceSchema.virtual('relatedDepartment', {
  ref: 'Department',
  localField: 'relatedDepartmentId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for file URL
EvidenceSchema.virtual('fileUrl').get(function() {
  if (this.filePath) {
    return `/uploads/evidence/${this.filePath}`;
  }
  return null;
});

// Static method to search evidence
EvidenceSchema.statics.searchEvidence = function(query, filters = {}) {
  const searchCriteria = {
    $and: [
      { isActive: true },
      ...(query ? [{ $text: { $search: query } }] : []),
    ],
  };

  // Apply filters
  if (filters.criterion) searchCriteria.$and.push({ criterion: filters.criterion });
  if (filters.type) searchCriteria.$and.push({ type: filters.type });
  if (filters.academicYear) searchCriteria.$and.push({ academicYear: filters.academicYear });
  if (filters.verificationStatus) searchCriteria.$and.push({ verificationStatus: filters.verificationStatus });
  if (filters.relatedProgramId) searchCriteria.$and.push({ relatedProgramId: filters.relatedProgramId });
  if (filters.relatedDepartmentId) searchCriteria.$and.push({ relatedDepartmentId: filters.relatedDepartmentId });

  return this.find(searchCriteria)
    .populate('relatedCourse', 'title code')
    .populate('relatedProgram', 'name code')
    .populate('relatedDepartment', 'name code')
    .populate('createdBy', 'name email')
    .populate('verifiedBy', 'name email')
    .sort({ createdAt: -1 });
};

// Static method to get evidence by criterion
EvidenceSchema.statics.getByCriterion = function(criterion, programId = null) {
  const filter = { criterion, isActive: true, verificationStatus: 'Verified' };
  if (programId) filter.relatedProgramId = programId;

  return this.find(filter)
    .populate('relatedCourse', 'title code')
    .populate('relatedProgram', 'name code')
    .sort({ dateOfEvidence: -1, createdAt: -1 });
};

// Instance method to increment view count
EvidenceSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  this.lastAccessed = new Date();
  return this.save();
};

// Instance method to increment download count
EvidenceSchema.methods.incrementDownloadCount = function() {
  this.downloadCount += 1;
  this.lastAccessed = new Date();
  return this.save();
};

// Instance method to verify evidence
EvidenceSchema.methods.verify = function(userId, status, comments = '') {
  this.verificationStatus = status;
  this.verifiedBy = userId;
  this.verifiedAt = new Date();
  this.verificationComments = comments;
  this.updatedBy = userId;
  return this.save();
};

module.exports = mongoose.model('Evidence', EvidenceSchema);
