import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const MappingModal = ({ isOpen, onClose, onSubmit, course }) => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('po');
  const [poMapping, setPoMapping] = useState([]);
  const [psoMapping, setPsoMapping] = useState([]);

  useEffect(() => {
    if (course) {
      setPoMapping(course.mapping?.po || []);
      setPsoMapping(course.mapping?.pso || []);
    }
  }, [course]);

  const handlePOMapping = (coIndex, poCode, level, weight) => {
    const existingIndex = poMapping.findIndex(
      m => m.coIndex === coIndex && m.poCode === poCode
    );

    if (level === 0) {
      // Remove mapping if level is 0
      if (existingIndex !== -1) {
        setPoMapping(poMapping.filter((_, i) => i !== existingIndex));
      }
    } else {
      const newMapping = {
        coIndex,
        poCode,
        level: parseInt(level),
        weight: parseFloat(weight) || 0.5,
      };

      if (existingIndex !== -1) {
        // Update existing mapping
        const updated = [...poMapping];
        updated[existingIndex] = newMapping;
        setPoMapping(updated);
      } else {
        // Add new mapping
        setPoMapping([...poMapping, newMapping]);
      }
    }
  };

  const handlePSOMapping = (coIndex, psoCode, level, weight) => {
    const existingIndex = psoMapping.findIndex(
      m => m.coIndex === coIndex && m.psoCode === psoCode
    );

    if (level === 0) {
      // Remove mapping if level is 0
      if (existingIndex !== -1) {
        setPsoMapping(psoMapping.filter((_, i) => i !== existingIndex));
      }
    } else {
      const newMapping = {
        coIndex,
        psoCode,
        level: parseInt(level),
        weight: parseFloat(weight) || 0.5,
      };

      if (existingIndex !== -1) {
        // Update existing mapping
        const updated = [...psoMapping];
        updated[existingIndex] = newMapping;
        setPsoMapping(updated);
      } else {
        // Add new mapping
        setPsoMapping([...psoMapping, newMapping]);
      }
    }
  };

  const getMappingValue = (mappings, coIndex, code) => {
    const mapping = mappings.find(m => m.coIndex === coIndex && (m.poCode === code || m.psoCode === code));
    return mapping ? mapping.level : 0;
  };

  const getMappingWeight = (mappings, coIndex, code) => {
    const mapping = mappings.find(m => m.coIndex === coIndex && (m.poCode === code || m.psoCode === code));
    return mapping ? mapping.weight : 0.5;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setLoading(true);
    try {
      await onSubmit({
        po: poMapping,
        pso: psoMapping,
      });
    } catch (error) {
      console.error('Error submitting mapping:', error);
    } finally {
      setLoading(false);
    }
  };

  const levelColors = {
    0: 'bg-gray-100 text-gray-800',
    1: 'bg-green-100 text-green-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-red-100 text-red-800',
  };

  const levelLabels = {
    0: 'None',
    1: 'Low',
    2: 'Medium', 
    3: 'High',
  };

  if (!course?.program) {
    return null;
  }

  const cos = course.cos || [];
  const pos = course.program.pos || [];
  const psos = course.program.psos || [];

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-7xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      CO-PO/PSO Mapping
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      {course.code} - {course.title}
                    </p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {cos.length === 0 ? (
                  <div className="text-center py-8">
                    <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No Course Outcomes</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Please define Course Outcomes first before creating CO-PO/PSO mapping.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Tabs */}
                    <div className="border-b border-gray-200">
                      <nav className="-mb-px flex space-x-8">
                        <button
                          type="button"
                          onClick={() => setActiveTab('po')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm ${
                            activeTab === 'po'
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          CO-PO Mapping ({pos.length} POs)
                        </button>
                        <button
                          type="button"
                          onClick={() => setActiveTab('pso')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm ${
                            activeTab === 'pso'
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          CO-PSO Mapping ({psos.length} PSOs)
                        </button>
                      </nav>
                    </div>

                    {/* Mapping Legend */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Mapping Levels</h4>
                      <div className="flex space-x-4 text-sm">
                        {Object.entries(levelLabels).map(([level, label]) => (
                          <div key={level} className="flex items-center space-x-1">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${levelColors[level]}`}>
                              {level}
                            </span>
                            <span className="text-gray-600">{label}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* CO-PO Mapping */}
                    {activeTab === 'po' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-medium text-gray-900">Course Outcome to Program Outcome Mapping</h4>
                        
                        {pos.length === 0 ? (
                          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <p className="text-gray-500">No Program Outcomes defined for this program.</p>
                          </div>
                        ) : (
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Course Outcome
                                  </th>
                                  {pos.map((po) => (
                                    <th key={po.code} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {po.code}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {cos.map((co) => (
                                  <tr key={co.index}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="text-sm font-medium text-gray-900">CO{co.index}</div>
                                      <div className="text-sm text-gray-500 max-w-xs truncate" title={co.statement}>
                                        {co.statement}
                                      </div>
                                    </td>
                                    {pos.map((po) => (
                                      <td key={`${co.index}-${po.code}`} className="px-3 py-4 text-center">
                                        <div className="space-y-2">
                                          <select
                                            value={getMappingValue(poMapping, co.index, po.code)}
                                            onChange={(e) => handlePOMapping(
                                              co.index,
                                              po.code,
                                              e.target.value,
                                              getMappingWeight(poMapping, co.index, po.code)
                                            )}
                                            className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                          >
                                            <option value="0">0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                          </select>
                                          {getMappingValue(poMapping, co.index, po.code) > 0 && (
                                            <input
                                              type="number"
                                              min="0"
                                              max="1"
                                              step="0.1"
                                              value={getMappingWeight(poMapping, co.index, po.code)}
                                              onChange={(e) => handlePOMapping(
                                                co.index,
                                                po.code,
                                                getMappingValue(poMapping, co.index, po.code),
                                                e.target.value
                                              )}
                                              placeholder="Weight"
                                              className="block w-full text-xs border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                            />
                                          )}
                                        </div>
                                      </td>
                                    ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    )}

                    {/* CO-PSO Mapping */}
                    {activeTab === 'pso' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-medium text-gray-900">Course Outcome to Program Specific Outcome Mapping</h4>
                        
                        {psos.length === 0 ? (
                          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <p className="text-gray-500">No Program Specific Outcomes defined for this program.</p>
                          </div>
                        ) : (
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Course Outcome
                                  </th>
                                  {psos.map((pso) => (
                                    <th key={pso.code} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {pso.code}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {cos.map((co) => (
                                  <tr key={co.index}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="text-sm font-medium text-gray-900">CO{co.index}</div>
                                      <div className="text-sm text-gray-500 max-w-xs truncate" title={co.statement}>
                                        {co.statement}
                                      </div>
                                    </td>
                                    {psos.map((pso) => (
                                      <td key={`${co.index}-${pso.code}`} className="px-3 py-4 text-center">
                                        <div className="space-y-2">
                                          <select
                                            value={getMappingValue(psoMapping, co.index, pso.code)}
                                            onChange={(e) => handlePSOMapping(
                                              co.index,
                                              pso.code,
                                              e.target.value,
                                              getMappingWeight(psoMapping, co.index, pso.code)
                                            )}
                                            className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                          >
                                            <option value="0">0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                          </select>
                                          {getMappingValue(psoMapping, co.index, pso.code) > 0 && (
                                            <input
                                              type="number"
                                              min="0"
                                              max="1"
                                              step="0.1"
                                              value={getMappingWeight(psoMapping, co.index, pso.code)}
                                              onChange={(e) => handlePSOMapping(
                                                co.index,
                                                pso.code,
                                                getMappingValue(psoMapping, co.index, pso.code),
                                                e.target.value
                                              )}
                                              placeholder="Weight"
                                              className="block w-full text-xs border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                            />
                                          )}
                                        </div>
                                      </td>
                                    ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Guidelines */}
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <h4 className="text-sm font-medium text-blue-900 mb-2">Mapping Guidelines</h4>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• <strong>Level 1 (Low):</strong> Slight correlation between CO and PO/PSO</li>
                        <li>• <strong>Level 2 (Medium):</strong> Moderate correlation between CO and PO/PSO</li>
                        <li>• <strong>Level 3 (High):</strong> Strong correlation between CO and PO/PSO</li>
                        <li>• <strong>Weight:</strong> Relative importance of this mapping (0.0 to 1.0)</li>
                        <li>• Map only relevant COs to POs/PSOs - not every CO needs to map to every PO/PSO</li>
                      </ul>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={onClose}
                        className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? 'Saving...' : 'Save Mapping'}
                      </button>
                    </div>
                  </form>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
