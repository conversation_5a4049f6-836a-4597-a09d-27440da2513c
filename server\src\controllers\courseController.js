const { body, query } = require('express-validator');
const Course = require('../models/Course');
const Program = require('../models/Program');
const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createCourseValidation = [
  body('programId')
    .isMongoId()
    .withMessage('Invalid program ID'),
  body('code')
    .trim()
    .isLength({ min: 3, max: 20 })
    .matches(/^[A-Z]{2,4}\d{3,4}[A-Z]?$/)
    .withMessage('Invalid course code format (e.g., CSE101, MATH201A)'),
  body('title')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Course title must be between 2 and 200 characters'),
  body('semester')
    .isInt({ min: 1, max: 20 })
    .withMessage('Semester must be between 1 and 20'),
  body('year')
    .isInt({ min: 2000, max: 2100 })
    .withMessage('Year must be between 2000 and 2100'),
  body('credits.total')
    .isInt({ min: 1 })
    .withMessage('Total credits must be at least 1'),
  body('type')
    .isIn(['Core', 'Elective', 'Open Elective', 'Project', 'Internship', 'Seminar'])
    .withMessage('Invalid course type'),
  body('category')
    .isIn(['Theory', 'Practical', 'Theory + Practical'])
    .withMessage('Invalid course category'),
  body('coordinator')
    .isMongoId()
    .withMessage('Invalid coordinator ID'),
];

const updateCourseValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Course title must be between 2 and 200 characters'),
  body('credits.total')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Total credits must be at least 1'),
  body('type')
    .optional()
    .isIn(['Core', 'Elective', 'Open Elective', 'Project', 'Internship', 'Seminar'])
    .withMessage('Invalid course type'),
  body('category')
    .optional()
    .isIn(['Theory', 'Practical', 'Theory + Practical'])
    .withMessage('Invalid course category'),
];

// Get all courses with filtering
const getCourses = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      programId,
      semester,
      year,
      type,
      facultyId,
      search,
    } = req.query;

    // Build filter object
    const filter = { isActive: true };
    
    if (programId) filter.programId = programId;
    if (semester) filter.semester = parseInt(semester);
    if (year) filter.year = parseInt(year);
    if (type) filter.type = type;
    if (facultyId) {
      filter.$or = [
        { facultyIds: facultyId },
        { coordinator: facultyId }
      ];
    }
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    // For non-admin users, filter by their department's programs
    if (req.user.role !== 'ADMIN') {
      const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
      const programIds = programs.map(p => p._id);
      filter.programId = { $in: programIds };
    }

    const skip = (page - 1) * limit;

    const [courses, total] = await Promise.all([
      Course.find(filter)
        .populate('program', 'name code level')
        .populate('faculty', 'name email')
        .populate('coordinatorDetails', 'name email')
        .sort({ year: -1, semester: 1, code: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Course.countDocuments(filter),
    ]);

    // Get statistics for each course
    const coursesWithStats = await Promise.all(
      courses.map(async (course) => {
        const stats = await course.getStatistics();
        return {
          ...course.toObject(),
          statistics: stats,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Courses retrieved successfully', coursesWithStats, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get course by ID
const getCourseById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const course = await Course.findById(id)
      .populate('program', 'name code level pos psos')
      .populate('faculty', 'name email phone designation')
      .populate('coordinatorDetails', 'name email phone designation');

    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    const program = await Program.findById(course.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const statistics = await course.getStatistics();
    const mappingValidation = await course.validateMapping();

    return sendSuccess(res, 'Course retrieved successfully', {
      ...course.toObject(),
      statistics,
      mappingValidation,
    });
  } catch (error) {
    return next(error);
  }
};

// Create new course
const createCourse = async (req, res, next) => {
  try {
    const {
      programId,
      code,
      title,
      description,
      semester,
      year,
      credits,
      type,
      category,
      facultyIds,
      coordinator,
      cos,
      schedule,
      enrollment,
      syllabus,
    } = req.body;

    // Validate program
    const program = await Program.findById(programId);
    if (!program || !program.isActive) {
      return next(new AppError('Invalid or inactive program', 400));
    }

    // Check department access
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied to this program', 403));
    }

    // Check if course code already exists for the year
    const existingCourse = await Course.findByCodeAndYear(code, year);
    if (existingCourse) {
      return next(new AppError('Course with this code already exists for the specified year', 409));
    }

    // Validate coordinator
    const coordinatorUser = await User.findById(coordinator);
    if (!coordinatorUser || coordinatorUser.deptId.toString() !== program.deptId.toString()) {
      return next(new AppError('Invalid coordinator or coordinator not from same department', 400));
    }

    // Validate faculty members
    if (facultyIds && facultyIds.length > 0) {
      const faculty = await User.find({ _id: { $in: facultyIds }, deptId: program.deptId });
      if (faculty.length !== facultyIds.length) {
        return next(new AppError('Some faculty members are invalid or not from same department', 400));
      }
    }

    const course = new Course({
      programId,
      code: code.toUpperCase(),
      title,
      description,
      semester,
      year,
      credits,
      type,
      category,
      facultyIds: facultyIds || [],
      coordinator,
      cos: cos || [],
      schedule,
      enrollment,
      syllabus,
      createdBy: req.user._id,
    });

    await course.save();

    const populatedCourse = await Course.findById(course._id)
      .populate('program', 'name code')
      .populate('faculty', 'name email')
      .populate('coordinatorDetails', 'name email');

    return sendCreated(res, 'Course created successfully', populatedCourse);
  } catch (error) {
    return next(error);
  }
};

// Update course
const updateCourse = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const course = await Course.findById(id);
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    const program = await Program.findById(course.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Faculty can only update courses they coordinate or teach
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    // Add updatedBy field
    updateData.updatedBy = req.user._id;

    const updatedCourse = await Course.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('program', 'name code')
     .populate('faculty', 'name email')
     .populate('coordinatorDetails', 'name email');

    return sendSuccess(res, 'Course updated successfully', updatedCourse);
  } catch (error) {
    return next(error);
  }
};

// Update Course Outcomes (COs)
const updateCOs = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { cos } = req.body;

    const course = await Course.findById(id);
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    const program = await Program.findById(course.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Faculty can only update courses they coordinate or teach
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    // Validate CO indices for uniqueness
    const coIndices = cos.map(co => co.index);
    const duplicates = coIndices.filter((index, i) => coIndices.indexOf(index) !== i);
    if (duplicates.length > 0) {
      return next(new AppError(`Duplicate CO indices found: ${duplicates.join(', ')}`, 400));
    }

    course.cos = cos;
    course.updatedBy = req.user._id;
    await course.save();

    return sendSuccess(res, 'Course Outcomes updated successfully', course.cos);
  } catch (error) {
    return next(error);
  }
};

// Update CO-PO/PSO mapping
const updateMapping = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { mapping } = req.body;

    const course = await Course.findById(id).populate('program');
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Faculty can only update courses they coordinate or teach
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    course.mapping = mapping;
    course.updatedBy = req.user._id;
    await course.save();

    // Validate the mapping
    const mappingValidation = await course.validateMapping();

    return sendSuccess(res, 'CO-PO/PSO mapping updated successfully', {
      mapping: course.mapping,
      validation: mappingValidation,
    });
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  updateCOs,
  updateMapping,
  createCourseValidation,
  updateCourseValidation,
  handleValidationErrors,
};
