const { body } = require('express-validator');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const Result = require('../models/Result');
const Assessment = require('../models/Assessment');
const Course = require('../models/Course');
const Program = require('../models/Program');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Configure multer for CSV upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads/results');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `results-${uniqueSuffix}.csv`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new AppError('Only CSV files are allowed', 400), false);
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Validation rules
const createResultValidation = [
  body('assessmentId')
    .isMongoId()
    .withMessage('Invalid assessment ID'),
  body('studentRoll')
    .trim()
    .matches(/^\d{4}[A-Z]{2,4}\d{3}$/)
    .withMessage('Invalid roll number format (e.g., 2020CSE001)'),
  body('studentName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Student name must be between 2 and 100 characters'),
  body('obtained')
    .isFloat({ min: 0 })
    .withMessage('Obtained marks must be non-negative'),
];

// Get all results with filtering
const getResults = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      assessmentId,
      courseId,
      studentRoll,
      status,
      year,
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (assessmentId) filter.assessmentId = assessmentId;
    if (studentRoll) filter.studentRoll = studentRoll.toUpperCase();
    if (status) filter.status = status;

    // For course-based filtering
    if (courseId) {
      const assessments = await Assessment.find({ courseId, isActive: true }).select('_id');
      const assessmentIds = assessments.map(a => a._id);
      filter.assessmentId = { $in: assessmentIds };
    }

    // For non-admin users, filter by their department's courses
    if (req.user.role !== 'ADMIN') {
      let allowedAssessmentIds = [];

      if (req.user.role === 'FACULTY') {
        // Faculty can see results for courses they teach or coordinate
        const courses = await Course.find({
          $or: [
            { facultyIds: req.user._id },
            { coordinator: req.user._id }
          ],
          ...(year && { year: parseInt(year) }),
          isActive: true
        }).select('_id');
        
        const courseIds = courses.map(c => c._id);
        const assessments = await Assessment.find({ 
          courseId: { $in: courseIds }, 
          isActive: true 
        }).select('_id');
        allowedAssessmentIds = assessments.map(a => a._id);
      } else {
        // Dept Head can see all results from their department
        const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
        const programIds = programs.map(p => p._id);
        const courses = await Course.find({ 
          programId: { $in: programIds },
          ...(year && { year: parseInt(year) }),
          isActive: true 
        }).select('_id');
        const courseIds = courses.map(c => c._id);
        const assessments = await Assessment.find({ 
          courseId: { $in: courseIds }, 
          isActive: true 
        }).select('_id');
        allowedAssessmentIds = assessments.map(a => a._id);
      }

      if (filter.assessmentId && filter.assessmentId.$in) {
        // Intersection of existing filter and allowed assessments
        filter.assessmentId = { 
          $in: filter.assessmentId.$in.filter(id => 
            allowedAssessmentIds.some(allowedId => allowedId.toString() === id.toString())
          )
        };
      } else if (filter.assessmentId) {
        // Check if single assessment ID is allowed
        const isAllowed = allowedAssessmentIds.some(id => 
          id.toString() === filter.assessmentId.toString()
        );
        if (!isAllowed) {
          return sendSuccess(res, 'Results retrieved successfully', [], {
            pagination: { currentPage: 1, totalPages: 0, totalItems: 0, itemsPerPage: parseInt(limit) }
          });
        }
      } else {
        filter.assessmentId = { $in: allowedAssessmentIds };
      }
    }

    const skip = (page - 1) * limit;

    const [results, total] = await Promise.all([
      Result.find(filter)
        .populate({
          path: 'assessment',
          select: 'title type maxMarks date courseId',
          populate: {
            path: 'course',
            select: 'title code semester year programId',
            populate: {
              path: 'program',
              select: 'name code'
            }
          }
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Result.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Results retrieved successfully', results, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get result by ID
const getResultById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const result = await Result.findById(id)
      .populate({
        path: 'assessment',
        select: 'title type maxMarks date courseId questions coTags',
        populate: {
          path: 'course',
          select: 'title code semester year programId cos',
          populate: {
            path: 'program',
            select: 'name code'
          }
        }
      });

    if (!result) {
      return sendNotFound(res, 'Result not found');
    }

    // Check access permissions
    const course = result.assessment.course;
    const program = course.program;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds?.includes(req.user._id) || 
                       course.coordinator?.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && program.deptId?.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Calculate CO attainment for this result
    const coAttainment = result.calculateCOAttainment();

    return sendSuccess(res, 'Result retrieved successfully', {
      ...result.toObject(),
      coAttainment,
    });
  } catch (error) {
    return next(error);
  }
};

// Create single result
const createResult = async (req, res, next) => {
  try {
    const {
      assessmentId,
      studentRoll,
      studentName,
      obtained,
      questionWise,
      remarks,
    } = req.body;

    // Validate assessment
    const assessment = await Assessment.findById(assessmentId).populate({
      path: 'course',
      populate: { path: 'program' }
    });
    
    if (!assessment || !assessment.isActive) {
      return next(new AppError('Invalid or inactive assessment', 400));
    }

    // Check access permissions
    const course = assessment.course;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Check if result already exists
    const existingResult = await Result.findOne({ assessmentId, studentRoll: studentRoll.toUpperCase() });
    if (existingResult) {
      return next(new AppError('Result already exists for this student and assessment', 409));
    }

    // Validate obtained marks
    if (obtained > assessment.maxMarks) {
      return next(new AppError('Obtained marks cannot exceed maximum marks', 400));
    }

    const result = new Result({
      assessmentId,
      studentRoll: studentRoll.toUpperCase(),
      studentName,
      obtained,
      questionWise: questionWise || [],
      remarks,
      createdBy: req.user._id,
    });

    await result.save();

    const populatedResult = await Result.findById(result._id)
      .populate('assessment', 'title type maxMarks');

    return sendCreated(res, 'Result created successfully', populatedResult);
  } catch (error) {
    return next(error);
  }
};

// Bulk upload results via CSV
const bulkUploadResults = async (req, res, next) => {
  try {
    const { assessmentId } = req.body;

    if (!req.file) {
      return next(new AppError('CSV file is required', 400));
    }

    // Validate assessment
    const assessment = await Assessment.findById(assessmentId).populate({
      path: 'course',
      populate: { path: 'program' }
    });
    
    if (!assessment || !assessment.isActive) {
      return next(new AppError('Invalid or inactive assessment', 400));
    }

    // Check access permissions
    const course = assessment.course;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const results = [];
    const errors = [];
    let rowNumber = 1;

    // Parse CSV file
    const csvData = await new Promise((resolve, reject) => {
      const data = [];
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('data', (row) => data.push(row))
        .on('end', () => resolve(data))
        .on('error', reject);
    });

    // Process each row
    for (const row of csvData) {
      rowNumber++;
      
      try {
        const { studentRoll, studentName, obtained, ...questionMarks } = row;

        // Validate required fields
        if (!studentRoll || !studentName || obtained === undefined) {
          errors.push({
            row: rowNumber,
            error: 'Missing required fields: studentRoll, studentName, or obtained',
            data: row
          });
          continue;
        }

        // Validate roll number format
        if (!/^\d{4}[A-Z]{2,4}\d{3}$/.test(studentRoll.trim())) {
          errors.push({
            row: rowNumber,
            error: 'Invalid roll number format',
            data: row
          });
          continue;
        }

        // Validate obtained marks
        const obtainedMarks = parseFloat(obtained);
        if (isNaN(obtainedMarks) || obtainedMarks < 0 || obtainedMarks > assessment.maxMarks) {
          errors.push({
            row: rowNumber,
            error: `Invalid obtained marks: ${obtained}`,
            data: row
          });
          continue;
        }

        // Check for duplicate
        const existingResult = await Result.findOne({ 
          assessmentId, 
          studentRoll: studentRoll.trim().toUpperCase() 
        });
        
        if (existingResult) {
          errors.push({
            row: rowNumber,
            error: 'Result already exists for this student',
            data: row
          });
          continue;
        }

        // Process question-wise marks
        const questionWise = [];
        Object.keys(questionMarks).forEach(key => {
          if (key.startsWith('Q') && questionMarks[key]) {
            const questionNumber = parseInt(key.substring(1));
            const marks = parseFloat(questionMarks[key]);
            
            if (!isNaN(questionNumber) && !isNaN(marks)) {
              // Find corresponding question in assessment
              const question = assessment.questions.find(q => q.questionNumber === questionNumber);
              if (question) {
                questionWise.push({
                  q: questionNumber,
                  marks: marks,
                  maxMarks: question.marks,
                  coIndex: question.coIndex,
                });
              }
            }
          }
        });

        const result = new Result({
          assessmentId,
          studentRoll: studentRoll.trim().toUpperCase(),
          studentName: studentName.trim(),
          obtained: obtainedMarks,
          questionWise,
          createdBy: req.user._id,
        });

        await result.save();
        results.push(result);

      } catch (error) {
        errors.push({
          row: rowNumber,
          error: error.message,
          data: row
        });
      }
    }

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    // Update assessment statistics
    if (results.length > 0) {
      await assessment.updateStatistics();
    }

    return sendSuccess(res, 'Bulk upload completed', {
      totalProcessed: csvData.length,
      successfulUploads: results.length,
      errors: errors.length,
      results: results.slice(0, 10), // Return first 10 results
      errorDetails: errors.slice(0, 20), // Return first 20 errors
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    return next(error);
  }
};

module.exports = {
  getResults,
  getResultById,
  createResult,
  bulkUploadResults,
  upload,
  createResultValidation,
  handleValidationErrors,
};
