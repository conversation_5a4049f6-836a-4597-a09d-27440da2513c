const ReadinessMetric = require('../models/ReadinessMetric');
const Evidence = require('../models/Evidence');
const Program = require('../models/Program');
const Course = require('../models/Course');
const User = require('../models/User');
const obeCalculationService = require('./obeCalculationService');

class ReadinessCalculationService {
  /**
   * Calculate overall NBA readiness for a program
   * @param {string} programId - Program ID
   * @param {string} academicYear - Academic year
   * @returns {Object} NBA readiness data
   */
  async calculateProgramReadiness(programId, academicYear) {
    try {
      const program = await Program.findById(programId).populate('department');
      if (!program) {
        throw new Error('Program not found');
      }

      // Get all readiness metrics for the program
      const metrics = await ReadinessMetric.getByProgram(programId);

      // Calculate category-wise readiness
      const categoryReadiness = await this.calculateCategoryReadiness(metrics);

      // Calculate overall readiness
      const overallReadiness = await this.calculateOverallReadiness(metrics);

      // Get evidence coverage
      const evidenceCoverage = await this.calculateEvidenceCoverage(programId);

      // Get OBE attainment data
      const obeAttainment = await this.getOBEAttainmentSummary(programId, academicYear);

      // Calculate compliance status
      const complianceStatus = this.calculateComplianceStatus(
        categoryReadiness, 
        evidenceCoverage, 
        obeAttainment
      );

      // Generate recommendations
      const recommendations = this.generateRecommendations(
        categoryReadiness, 
        evidenceCoverage, 
        obeAttainment,
        metrics
      );

      return {
        programId,
        programName: program.name,
        programCode: program.code,
        department: program.department,
        academicYear,
        overallReadiness: {
          percentage: overallReadiness,
          status: this.getReadinessStatus(overallReadiness),
          lastUpdated: new Date()
        },
        categoryReadiness,
        evidenceCoverage,
        obeAttainment,
        complianceStatus,
        recommendations,
        totalMetrics: metrics.length,
        calculatedAt: new Date()
      };

    } catch (error) {
      throw new Error(`NBA readiness calculation failed: ${error.message}`);
    }
  }

  /**
   * Calculate readiness by NBA category
   */
  async calculateCategoryReadiness(metrics) {
    const categories = {};

    // Group metrics by category
    metrics.forEach(metric => {
      if (!categories[metric.category]) {
        categories[metric.category] = {
          metrics: [],
          totalWeight: 0,
          weightedScore: 0
        };
      }
      categories[metric.category].metrics.push(metric);
      categories[metric.category].totalWeight += metric.weight;
      categories[metric.category].weightedScore += metric.readinessPercentage * metric.weight;
    });

    // Calculate category percentages
    Object.keys(categories).forEach(categoryName => {
      const category = categories[categoryName];
      category.percentage = category.totalWeight > 0 ? 
        Math.round((category.weightedScore / category.totalWeight) * 100) / 100 : 0;
      category.status = this.getReadinessStatus(category.percentage);
      category.metricsCount = category.metrics.length;
      category.criticalMetrics = category.metrics.filter(m => m.status === 'Critical').length;
      category.excellentMetrics = category.metrics.filter(m => m.status === 'Excellent').length;
    });

    return categories;
  }

  /**
   * Calculate overall readiness using weighted average
   */
  async calculateOverallReadiness(metrics) {
    if (metrics.length === 0) return 0;

    const totalWeightedScore = metrics.reduce((sum, metric) => {
      return sum + (metric.readinessPercentage * metric.weight);
    }, 0);

    const totalWeight = metrics.reduce((sum, metric) => sum + metric.weight, 0);

    return totalWeight > 0 ? Math.round((totalWeightedScore / totalWeight) * 100) / 100 : 0;
  }

  /**
   * Calculate evidence coverage for NBA criteria
   */
  async calculateEvidenceCoverage(programId) {
    const nbaCriteria = [
      'VisionMission',
      'ProgramOutcomes',
      'Curriculum',
      'TeachingLearning',
      'Infra',
      'StudentPerformance',
      'Faculty',
      'Finance',
      'ContinuousImprovement'
    ];

    const coverage = {};

    for (const criterion of nbaCriteria) {
      const evidenceCount = await Evidence.countDocuments({
        criterion,
        relatedProgramId: programId,
        verificationStatus: 'Verified',
        isActive: true
      });

      const totalEvidenceCount = await Evidence.countDocuments({
        criterion,
        relatedProgramId: programId,
        isActive: true
      });

      coverage[criterion] = {
        verifiedEvidence: evidenceCount,
        totalEvidence: totalEvidenceCount,
        coveragePercentage: totalEvidenceCount > 0 ? 
          Math.round((evidenceCount / totalEvidenceCount) * 100) : 0,
        status: this.getEvidenceStatus(evidenceCount, totalEvidenceCount)
      };
    }

    // Calculate overall evidence coverage
    const totalVerified = Object.values(coverage).reduce((sum, c) => sum + c.verifiedEvidence, 0);
    const totalEvidence = Object.values(coverage).reduce((sum, c) => sum + c.totalEvidence, 0);

    return {
      criteria: coverage,
      overall: {
        verifiedEvidence: totalVerified,
        totalEvidence: totalEvidence,
        coveragePercentage: totalEvidence > 0 ? 
          Math.round((totalVerified / totalEvidence) * 100) : 0
      }
    };
  }

  /**
   * Get OBE attainment summary for readiness calculation
   */
  async getOBEAttainmentSummary(programId, academicYear) {
    try {
      const programAttainment = await obeCalculationService.calculateProgramAttainment(
        programId, 
        academicYear
      );

      const poAttainmentValues = Object.values(programAttainment.poAttainment);
      const psoAttainmentValues = Object.values(programAttainment.psoAttainment);

      const poStats = this.calculateAttainmentStats(poAttainmentValues.map(po => po.finalPercentage));
      const psoStats = this.calculateAttainmentStats(psoAttainmentValues.map(pso => pso.finalPercentage));

      return {
        poAttainment: {
          totalPOs: poAttainmentValues.length,
          attainedPOs: poAttainmentValues.filter(po => po.attained).length,
          averageAttainment: poStats.average,
          minAttainment: poStats.min,
          maxAttainment: poStats.max,
          attainmentPercentage: poAttainmentValues.length > 0 ? 
            (poAttainmentValues.filter(po => po.attained).length / poAttainmentValues.length) * 100 : 0
        },
        psoAttainment: {
          totalPSOs: psoAttainmentValues.length,
          attainedPSOs: psoAttainmentValues.filter(pso => pso.attained).length,
          averageAttainment: psoStats.average,
          minAttainment: psoStats.min,
          maxAttainment: psoStats.max,
          attainmentPercentage: psoAttainmentValues.length > 0 ? 
            (psoAttainmentValues.filter(pso => pso.attained).length / psoAttainmentValues.length) * 100 : 0
        },
        overallOBEScore: (poStats.average + psoStats.average) / 2
      };

    } catch (error) {
      console.error('Error calculating OBE attainment for readiness:', error.message);
      return {
        poAttainment: { totalPOs: 0, attainedPOs: 0, averageAttainment: 0, attainmentPercentage: 0 },
        psoAttainment: { totalPSOs: 0, attainedPSOs: 0, averageAttainment: 0, attainmentPercentage: 0 },
        overallOBEScore: 0,
        error: error.message
      };
    }
  }

  /**
   * Calculate compliance status based on various factors
   */
  calculateComplianceStatus(categoryReadiness, evidenceCoverage, obeAttainment) {
    const factors = [];

    // Check category readiness
    Object.keys(categoryReadiness).forEach(category => {
      const readiness = categoryReadiness[category];
      factors.push({
        factor: `${category} Readiness`,
        score: readiness.percentage,
        weight: 0.6 / Object.keys(categoryReadiness).length,
        status: readiness.status
      });
    });

    // Check evidence coverage
    factors.push({
      factor: 'Evidence Coverage',
      score: evidenceCoverage.overall.coveragePercentage,
      weight: 0.2,
      status: this.getReadinessStatus(evidenceCoverage.overall.coveragePercentage)
    });

    // Check OBE attainment
    factors.push({
      factor: 'OBE Attainment',
      score: obeAttainment.overallOBEScore,
      weight: 0.2,
      status: this.getReadinessStatus(obeAttainment.overallOBEScore)
    });

    // Calculate weighted compliance score
    const totalWeightedScore = factors.reduce((sum, factor) => {
      return sum + (factor.score * factor.weight);
    }, 0);

    const compliancePercentage = Math.round(totalWeightedScore * 100) / 100;

    return {
      overallCompliance: compliancePercentage,
      status: this.getComplianceStatus(compliancePercentage),
      factors,
      recommendations: this.generateComplianceRecommendations(factors)
    };
  }

  /**
   * Generate recommendations for improving NBA readiness
   */
  generateRecommendations(categoryReadiness, evidenceCoverage, obeAttainment, metrics) {
    const recommendations = [];

    // Category-based recommendations
    Object.keys(categoryReadiness).forEach(category => {
      const readiness = categoryReadiness[category];
      if (readiness.percentage < 70) {
        recommendations.push({
          type: 'category',
          priority: readiness.percentage < 50 ? 'High' : 'Medium',
          category,
          title: `Improve ${category} Readiness`,
          description: `Current readiness is ${readiness.percentage}%. Focus on improving metrics in this category.`,
          suggestedActions: this.getCategorySpecificActions(category, readiness.metrics)
        });
      }
    });

    // Evidence-based recommendations
    Object.keys(evidenceCoverage.criteria).forEach(criterion => {
      const coverage = evidenceCoverage.criteria[criterion];
      if (coverage.coveragePercentage < 80) {
        recommendations.push({
          type: 'evidence',
          priority: coverage.coveragePercentage < 50 ? 'High' : 'Medium',
          criterion,
          title: `Improve Evidence Coverage for ${criterion}`,
          description: `Only ${coverage.verifiedEvidence} out of ${coverage.totalEvidence} evidence items are verified.`,
          suggestedActions: [
            'Upload missing evidence documents',
            'Verify pending evidence items',
            'Ensure evidence quality and relevance'
          ]
        });
      }
    });

    // OBE-based recommendations
    if (obeAttainment.poAttainment.attainmentPercentage < 80) {
      recommendations.push({
        type: 'obe',
        priority: 'High',
        title: 'Improve Program Outcome Attainment',
        description: `Only ${obeAttainment.poAttainment.attainedPOs} out of ${obeAttainment.poAttainment.totalPOs} POs are attained.`,
        suggestedActions: [
          'Review and strengthen CO-PO mappings',
          'Improve assessment strategies',
          'Enhance teaching methodologies',
          'Provide additional student support'
        ]
      });
    }

    // Critical metrics recommendations
    const criticalMetrics = metrics.filter(m => m.status === 'Critical');
    if (criticalMetrics.length > 0) {
      recommendations.push({
        type: 'critical',
        priority: 'Critical',
        title: 'Address Critical Metrics',
        description: `${criticalMetrics.length} metrics are in critical status and need immediate attention.`,
        suggestedActions: criticalMetrics.map(m => `Improve ${m.name}: ${m.description}`).slice(0, 5)
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { 'Critical': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Get category-specific improvement actions
   */
  getCategorySpecificActions(category, metrics) {
    const actions = {
      'Faculty': [
        'Recruit qualified faculty members',
        'Provide professional development opportunities',
        'Improve faculty-student ratio',
        'Enhance faculty research activities'
      ],
      'Curriculum': [
        'Update curriculum to industry standards',
        'Improve CO-PO mapping coverage',
        'Enhance practical components',
        'Include emerging technologies'
      ],
      'StudentPerformance': [
        'Implement student support programs',
        'Improve assessment strategies',
        'Enhance learning resources',
        'Monitor student progress regularly'
      ],
      'Infrastructure': [
        'Upgrade laboratory facilities',
        'Improve library resources',
        'Enhance IT infrastructure',
        'Provide better learning spaces'
      ],
      'ContinuousImprovement': [
        'Establish feedback mechanisms',
        'Implement quality assurance processes',
        'Regular curriculum review',
        'Stakeholder engagement programs'
      ]
    };

    return actions[category] || [
      'Review current practices',
      'Identify improvement areas',
      'Implement best practices',
      'Monitor progress regularly'
    ];
  }

  /**
   * Helper methods
   */
  getReadinessStatus(percentage) {
    if (percentage >= 90) return 'Excellent';
    if (percentage >= 80) return 'Very Good';
    if (percentage >= 70) return 'Good';
    if (percentage >= 60) return 'Satisfactory';
    if (percentage >= 50) return 'Needs Improvement';
    return 'Critical';
  }

  getEvidenceStatus(verified, total) {
    if (total === 0) return 'No Evidence';
    const percentage = (verified / total) * 100;
    return this.getReadinessStatus(percentage);
  }

  getComplianceStatus(percentage) {
    if (percentage >= 85) return 'Fully Compliant';
    if (percentage >= 70) return 'Largely Compliant';
    if (percentage >= 50) return 'Partially Compliant';
    return 'Non-Compliant';
  }

  calculateAttainmentStats(values) {
    if (values.length === 0) {
      return { average: 0, min: 0, max: 0 };
    }

    const sum = values.reduce((acc, val) => acc + val, 0);
    return {
      average: Math.round((sum / values.length) * 100) / 100,
      min: Math.min(...values),
      max: Math.max(...values)
    };
  }
}

module.exports = new ReadinessCalculationService();
