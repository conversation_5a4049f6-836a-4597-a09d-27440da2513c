import { Navigate, useLocation } from "react-router-dom";
import { useAuthStore } from "../../stores/authStore";
import { LoadingSpinner } from "../ui/LoadingSpinner";

export const ProtectedRoute = ({ children, requiredRoles = [] }) => {
  const { isAuthenticated, user, isLoading } = useAuthStore();
  const location = useLocation();

  console.log('ProtectedRoute: State check:', { isAuthenticated, isLoading, user: user?.name, requiredRoles });

  // 🔄 Still checking authentication
  if (isLoading) {
    console.log('ProtectedRoute: Showing loading spinner');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // ❌ Not logged in
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 🔄 Logged in but user data not ready yet
  if (isAuthenticated && !user) {
    console.log('ProtectedRoute: Authenticated but no user data - this should not happen!');
    // This is an invalid state - we should not be authenticated without user data
    // Force logout to reset the state
    useAuthStore.getState().logout();
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 🚫 Role-based restriction
  if (requiredRoles.length > 0 && user && !requiredRoles.includes(user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-gray-500">
            Required roles: {requiredRoles.join(", ")}
          </p>
          <p className="text-sm text-gray-500">Your role: {user?.role}</p>
          <button
            onClick={() => window.history.back()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // ✅ Everything okay → allow
  return children;
};
