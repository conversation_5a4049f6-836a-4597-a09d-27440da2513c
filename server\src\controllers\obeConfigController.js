const { body } = require('express-validator');
const OBEConfig = require('../models/OBEConfig');
const Program = require('../models/Program');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createOBEConfigValidation = [
  body('programId')
    .isMongoId()
    .withMessage('Invalid program ID'),
  body('directWeight')
    .isFloat({ min: 0, max: 1 })
    .withMessage('Direct weight must be between 0 and 1'),
  body('indirectWeight')
    .isFloat({ min: 0, max: 1 })
    .withMessage('Indirect weight must be between 0 and 1'),
  body('targets.coTarget')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CO target must be between 0 and 100'),
  body('targets.poTarget')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('PO target must be between 0 and 100'),
  body('targets.psoTarget')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('PSO target must be between 0 and 100'),
];

// Get OBE config by program
const getOBEConfigByProgram = async (req, res, next) => {
  try {
    const { programId } = req.params;

    // Validate program exists
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    let config = await OBEConfig.getByProgram(programId);

    // If no config exists, return default config
    if (!config) {
      const defaultConfig = OBEConfig.getDefaultConfig();
      return sendSuccess(res, 'Default OBE configuration retrieved', {
        ...defaultConfig,
        programId,
        isDefault: true,
      });
    }

    return sendSuccess(res, 'OBE configuration retrieved successfully', config);
  } catch (error) {
    return next(error);
  }
};

// Create or update OBE config
const createOrUpdateOBEConfig = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const configData = req.body;

    // Validate program exists
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Check if config already exists
    let config = await OBEConfig.findOne({ programId });

    if (config) {
      // Update existing config
      Object.assign(config, configData);
      config.updatedBy = req.user._id;
      await config.save();

      return sendSuccess(res, 'OBE configuration updated successfully', config);
    } else {
      // Create new config
      config = new OBEConfig({
        ...configData,
        programId,
        createdBy: req.user._id,
      });

      await config.save();

      const populatedConfig = await OBEConfig.findById(config._id).populate('program', 'name code');

      return sendCreated(res, 'OBE configuration created successfully', populatedConfig);
    }
  } catch (error) {
    return next(error);
  }
};

// Get all OBE configs (Admin only)
const getAllOBEConfigs = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, deptId } = req.query;

    // Build filter
    const filter = { isActive: true };

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
      const programIds = programs.map(p => p._id);
      filter.programId = { $in: programIds };
    } else if (deptId) {
      const programs = await Program.find({ deptId }).select('_id');
      const programIds = programs.map(p => p._id);
      filter.programId = { $in: programIds };
    }

    const skip = (page - 1) * limit;

    const [configs, total] = await Promise.all([
      OBEConfig.find(filter)
        .populate('program', 'name code level deptId')
        .populate('program.department', 'name code')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      OBEConfig.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'OBE configurations retrieved successfully', configs, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Update specific OBE config sections
const updateAttainmentBands = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { attainmentBands } = req.body;

    const config = await OBEConfig.findOne({ programId });
    if (!config) {
      return sendNotFound(res, 'OBE configuration not found');
    }

    // Check access permissions
    const program = await Program.findById(programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    config.attainmentBands = attainmentBands;
    config.updatedBy = req.user._id;
    await config.save();

    return sendSuccess(res, 'Attainment bands updated successfully', config.attainmentBands);
  } catch (error) {
    return next(error);
  }
};

// Update assessment weights
const updateAssessmentWeights = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { assessmentWeights } = req.body;

    const config = await OBEConfig.findOne({ programId });
    if (!config) {
      return sendNotFound(res, 'OBE configuration not found');
    }

    // Check access permissions
    const program = await Program.findById(programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    config.assessmentWeights = assessmentWeights;
    config.updatedBy = req.user._id;
    await config.save();

    return sendSuccess(res, 'Assessment weights updated successfully', config.assessmentWeights);
  } catch (error) {
    return next(error);
  }
};

// Update targets
const updateTargets = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { targets } = req.body;

    const config = await OBEConfig.findOne({ programId });
    if (!config) {
      return sendNotFound(res, 'OBE configuration not found');
    }

    // Check access permissions
    const program = await Program.findById(programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    config.targets = { ...config.targets, ...targets };
    config.updatedBy = req.user._id;
    await config.save();

    return sendSuccess(res, 'Targets updated successfully', config.targets);
  } catch (error) {
    return next(error);
  }
};

// Reset to default configuration
const resetToDefault = async (req, res, next) => {
  try {
    const { programId } = req.params;

    // Validate program exists
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const defaultConfig = OBEConfig.getDefaultConfig();

    let config = await OBEConfig.findOne({ programId });

    if (config) {
      // Update existing config with default values
      Object.assign(config, defaultConfig);
      config.updatedBy = req.user._id;
      await config.save();
    } else {
      // Create new config with default values
      config = new OBEConfig({
        ...defaultConfig,
        programId,
        createdBy: req.user._id,
      });
      await config.save();
    }

    const populatedConfig = await OBEConfig.findById(config._id).populate('program', 'name code');

    return sendSuccess(res, 'OBE configuration reset to default successfully', populatedConfig);
  } catch (error) {
    return next(error);
  }
};

// Delete OBE config (Admin only)
const deleteOBEConfig = async (req, res, next) => {
  try {
    const { programId } = req.params;

    const config = await OBEConfig.findOne({ programId });
    if (!config) {
      return sendNotFound(res, 'OBE configuration not found');
    }

    config.isActive = false;
    config.updatedBy = req.user._id;
    await config.save();

    return sendSuccess(res, 'OBE configuration deleted successfully');
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getOBEConfigByProgram,
  createOrUpdateOBEConfig,
  getAllOBEConfigs,
  updateAttainmentBands,
  updateAssessmentWeights,
  updateTargets,
  resetToDefault,
  deleteOBEConfig,
  createOBEConfigValidation,
  handleValidationErrors,
};
