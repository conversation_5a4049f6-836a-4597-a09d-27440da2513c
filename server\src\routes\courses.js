const express = require('express');
const {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  updateCOs,
  updateMapping,
  createCourseValidation,
  updateCourseValidation,
  handleValidationErrors,
} = require('../controllers/courseController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all courses (with filtering and pagination)
router.get('/', getCourses);

// Get course by ID
router.get('/:id', getCourseById);

// Create new course (Admin, Dept Head, and Faculty)
router.post('/', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), createCourseValidation, handleValidationErrors, createCourse);

// Update course (Admin, Dept Head, and assigned Faculty)
router.put('/:id', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), updateCourseValidation, handleValidationErrors, updateCourse);

// Update Course Outcomes (Admin, Dept Head, and assigned Faculty)
router.put('/:id/cos', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), updateCOs);

// Update CO-PO/PSO mapping (Admin, Dept Head, and assigned Faculty)
router.put('/:id/mapping', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), updateMapping);

module.exports = router;
