const { body } = require('express-validator');
const ReadinessMetric = require('../models/ReadinessMetric');
const Program = require('../models/Program');
const readinessCalculationService = require('../services/readinessCalculationService');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createMetricValidation = [
  body('programId')
    .isMongoId()
    .withMessage('Invalid program ID'),
  body('code')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Metric code must be between 1 and 50 characters'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Metric name must be between 2 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('category')
    .isIn(['Faculty', 'Curriculum', 'StudentPerformance', 'Infrastructure', 'ContinuousImprovement', 'Research', 'Industry', 'Governance'])
    .withMessage('Invalid metric category'),
  body('currentValue')
    .isFloat({ min: 0 })
    .withMessage('Current value must be non-negative'),
  body('targetValue')
    .isFloat({ min: 0 })
    .withMessage('Target value must be non-negative'),
  body('weight')
    .isFloat({ min: 0, max: 1 })
    .withMessage('Weight must be between 0 and 1'),
];

// Get program readiness overview
const getProgramReadiness = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { academicYear } = req.query;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Use current academic year if not provided
    const targetAcademicYear = academicYear || getCurrentAcademicYear();

    const readiness = await readinessCalculationService.calculateProgramReadiness(
      programId, 
      targetAcademicYear
    );

    return sendSuccess(res, 'Program readiness calculated successfully', readiness);
  } catch (error) {
    return next(error);
  }
};

// Get all readiness metrics for a program
const getReadinessMetrics = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { category, priority, status } = req.query;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    let metrics = await ReadinessMetric.getByProgram(programId, category);

    // Apply additional filters
    if (priority) {
      metrics = metrics.filter(m => m.priority === priority);
    }
    if (status) {
      metrics = metrics.filter(m => m.status === status);
    }

    // Group metrics by category for better organization
    const groupedMetrics = {};
    metrics.forEach(metric => {
      if (!groupedMetrics[metric.category]) {
        groupedMetrics[metric.category] = [];
      }
      groupedMetrics[metric.category].push(metric);
    });

    return sendSuccess(res, 'Readiness metrics retrieved successfully', {
      programId,
      programName: program.name,
      totalMetrics: metrics.length,
      categories: Object.keys(groupedMetrics).length,
      metrics: groupedMetrics
    });
  } catch (error) {
    return next(error);
  }
};

// Get specific readiness metric
const getReadinessMetricById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const metric = await ReadinessMetric.findById(id)
      .populate('program', 'name code')
      .populate('evidence', 'title type verificationStatus')
      .populate('actionItems.assignedTo', 'name email');

    if (!metric) {
      return sendNotFound(res, 'Readiness metric not found');
    }

    // Check access permissions
    const program = await Program.findById(metric.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    return sendSuccess(res, 'Readiness metric retrieved successfully', metric);
  } catch (error) {
    return next(error);
  }
};

// Create new readiness metric
const createReadinessMetric = async (req, res, next) => {
  try {
    const {
      programId,
      code,
      name,
      description,
      category,
      subCategory,
      currentValue,
      targetValue,
      benchmarkValue,
      unit,
      weight,
      priority,
      nbaMapping,
      thresholds,
      dataSources,
      updateFrequency,
    } = req.body;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return next(new AppError('Invalid program', 400));
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Check if metric code already exists for the program
    const existingMetric = await ReadinessMetric.findOne({ 
      programId, 
      code: code.toUpperCase() 
    });
    if (existingMetric) {
      return next(new AppError('Metric with this code already exists for the program', 409));
    }

    const metric = new ReadinessMetric({
      programId,
      code: code.toUpperCase(),
      name,
      description,
      category,
      subCategory,
      currentValue,
      targetValue,
      benchmarkValue,
      unit,
      weight,
      priority,
      nbaMapping,
      thresholds,
      dataSources,
      updateFrequency,
      createdBy: req.user._id,
    });

    await metric.save();

    const populatedMetric = await ReadinessMetric.findById(metric._id)
      .populate('program', 'name code');

    return sendCreated(res, 'Readiness metric created successfully', populatedMetric);
  } catch (error) {
    return next(error);
  }
};

// Update readiness metric
const updateReadinessMetric = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const metric = await ReadinessMetric.findById(id);
    if (!metric) {
      return sendNotFound(res, 'Readiness metric not found');
    }

    // Check access permissions
    const program = await Program.findById(metric.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Add updatedBy field
    updateData.updatedBy = req.user._id;

    const updatedMetric = await ReadinessMetric.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('program', 'name code');

    return sendSuccess(res, 'Readiness metric updated successfully', updatedMetric);
  } catch (error) {
    return next(error);
  }
};

// Update metric value
const updateMetricValue = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { newValue, comments } = req.body;

    const metric = await ReadinessMetric.findById(id);
    if (!metric) {
      return sendNotFound(res, 'Readiness metric not found');
    }

    // Check access permissions
    const program = await Program.findById(metric.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate new value
    if (newValue < 0) {
      return next(new AppError('Value cannot be negative', 400));
    }

    await metric.updateValue(newValue, req.user._id, comments);

    return sendSuccess(res, 'Metric value updated successfully', {
      metricId: metric._id,
      previousValue: metric.historicalData[metric.historicalData.length - 1]?.value || 0,
      newValue: metric.currentValue,
      readinessPercentage: metric.readinessPercentage,
      status: metric.status
    });
  } catch (error) {
    return next(error);
  }
};

// Add action item to metric
const addActionItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { description, assignedTo, dueDate, priority } = req.body;

    const metric = await ReadinessMetric.findById(id);
    if (!metric) {
      return sendNotFound(res, 'Readiness metric not found');
    }

    // Check access permissions
    const program = await Program.findById(metric.programId);
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    await metric.addActionItem(description, assignedTo, dueDate, priority);

    return sendSuccess(res, 'Action item added successfully', {
      metricId: metric._id,
      actionItems: metric.actionItems
    });
  } catch (error) {
    return next(error);
  }
};

// Get readiness dashboard data
const getReadinessDashboard = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { academicYear } = req.query;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const targetAcademicYear = academicYear || getCurrentAcademicYear();

    // Get comprehensive readiness data
    const readiness = await readinessCalculationService.calculateProgramReadiness(
      programId, 
      targetAcademicYear
    );

    // Get metrics summary
    const metrics = await ReadinessMetric.getByProgram(programId);
    const metricsSummary = {
      total: metrics.length,
      byStatus: {},
      byCategory: {},
      byPriority: {},
      overdue: 0
    };

    metrics.forEach(metric => {
      // Count by status
      metricsSummary.byStatus[metric.status] = (metricsSummary.byStatus[metric.status] || 0) + 1;
      
      // Count by category
      metricsSummary.byCategory[metric.category] = (metricsSummary.byCategory[metric.category] || 0) + 1;
      
      // Count by priority
      metricsSummary.byPriority[metric.priority] = (metricsSummary.byPriority[metric.priority] || 0) + 1;
      
      // Count overdue metrics
      if (metric.nextUpdateDue && metric.nextUpdateDue < new Date()) {
        metricsSummary.overdue++;
      }
    });

    // Get recent action items
    const recentActionItems = metrics
      .flatMap(metric => metric.actionItems.map(item => ({
        ...item.toObject(),
        metricName: metric.name,
        metricCode: metric.code
      })))
      .filter(item => item.status === 'Pending')
      .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
      .slice(0, 10);

    return sendSuccess(res, 'Readiness dashboard data retrieved successfully', {
      ...readiness,
      metricsSummary,
      recentActionItems,
      lastCalculated: new Date()
    });
  } catch (error) {
    return next(error);
  }
};

// Get readiness trends over time
const getReadinessTrends = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { startYear, endYear, category } = req.query;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const start = parseInt(startYear) || new Date().getFullYear() - 2;
    const end = parseInt(endYear) || new Date().getFullYear();

    const trends = [];

    for (let year = start; year <= end; year++) {
      const academicYear = `${year}-${year + 1}`;
      
      try {
        const readiness = await readinessCalculationService.calculateProgramReadiness(
          programId, 
          academicYear
        );

        const trendData = {
          academicYear,
          year,
          overallReadiness: readiness.overallReadiness.percentage,
          categoryReadiness: {}
        };

        // Add category-specific data if requested
        if (category && readiness.categoryReadiness[category]) {
          trendData.categoryReadiness[category] = readiness.categoryReadiness[category].percentage;
        } else {
          // Add all categories
          Object.keys(readiness.categoryReadiness).forEach(cat => {
            trendData.categoryReadiness[cat] = readiness.categoryReadiness[cat].percentage;
          });
        }

        trends.push(trendData);
      } catch (error) {
        console.error(`Error calculating trends for ${academicYear}:`, error.message);
      }
    }

    return sendSuccess(res, 'Readiness trends retrieved successfully', {
      programId,
      programName: program.name,
      startYear: start,
      endYear: end,
      category: category || 'all',
      trends
    });
  } catch (error) {
    return next(error);
  }
};

// Helper function
function getCurrentAcademicYear() {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  // Assuming academic year starts in July
  if (currentMonth >= 7) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
}

module.exports = {
  getProgramReadiness,
  getReadinessMetrics,
  getReadinessMetricById,
  createReadinessMetric,
  updateReadinessMetric,
  updateMetricValue,
  addActionItem,
  getReadinessDashboard,
  getReadinessTrends,
  createMetricValidation,
  handleValidationErrors,
};
