const http = require('http');

const API_BASE_URL = 'http://localhost:3000/api';

function makeRequest(path, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api${path}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsedData });
        } catch (error) {
          reject(new Error(`Failed to parse response: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

async function testLogin(email, password, userType) {
  try {
    console.log(`\n🧪 Testing login for ${userType}:`);
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 Password: ${password}`);

    const response = await makeRequest('/auth/login', {
      email,
      password
    });

    if (response.status === 200 && response.data.success) {
      console.log(`✅ ${userType} login successful!`);
      console.log(`👤 User: ${response.data.data.user.name}`);
      console.log(`🎭 Role: ${response.data.data.user.role}`);
      console.log(`🏢 Department: ${response.data.data.user.department?.name || 'N/A'}`);
      console.log(`🔐 Token received: ${response.data.data.token ? 'Yes' : 'No'}`);
      return true;
    } else {
      console.log(`❌ ${userType} login failed:`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Message: ${response.data.message || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${userType} login error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting login tests...\n');
  
  const testCases = [
    { email: '<EMAIL>', password: 'admin123', userType: 'Admin' },
    { email: '<EMAIL>', password: 'dept123', userType: 'Department Head' },
    { email: '<EMAIL>', password: 'faculty123', userType: 'Faculty' }
  ];

  let successCount = 0;
  
  for (const testCase of testCases) {
    const success = await testLogin(testCase.email, testCase.password, testCase.userType);
    if (success) successCount++;
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 Test Results: ${successCount}/${testCases.length} successful`);
  
  if (successCount === testCases.length) {
    console.log('🎉 All login tests passed!');
  } else {
    console.log('⚠️  Some login tests failed. Please check the credentials and server.');
  }
}

// Run the tests
runTests().catch(console.error);
