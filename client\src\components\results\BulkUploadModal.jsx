import { useState, useRef } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, ArrowUpTrayIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const BulkUploadModal = ({ isOpen, onClose, onSubmit, assessments }) => {
  const [loading, setLoading] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState('');
  const [file, setFile] = useState(null);
  const [uploadMode, setUploadMode] = useState('csv'); // 'csv' or 'manual'
  const [manualData, setManualData] = useState('');
  const [previewData, setPreviewData] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef(null);

  const handleAssessmentChange = (e) => {
    setSelectedAssessment(e.target.value);
    setFile(null);
    setPreviewData([]);
    setShowPreview(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('Please select a CSV file');
        return;
      }
      setFile(selectedFile);
      parseCSVFile(selectedFile);
    }
  };

  const parseCSVFile = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const csv = e.target.result;
      const lines = csv.split('\n');
      const headers = lines[0].split(',').map(h => h.trim());
      
      // Validate headers
      const requiredHeaders = ['studentRoll', 'studentName', 'obtained'];
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
      
      if (missingHeaders.length > 0) {
        toast.error(`Missing required columns: ${missingHeaders.join(', ')}`);
        return;
      }

      const data = [];
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
          const values = line.split(',').map(v => v.trim());
          const row = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          
          // Validate required fields
          if (row.studentRoll && row.studentName && row.obtained !== '') {
            data.push({
              studentRoll: row.studentRoll,
              studentName: row.studentName,
              obtained: parseFloat(row.obtained) || 0,
              status: row.status || 'Pass',
              grade: row.grade || '',
              remarks: row.remarks || '',
              evaluationDate: row.evaluationDate || new Date().toISOString().split('T')[0],
            });
          }
        }
      }

      setPreviewData(data);
      setShowPreview(true);
    };
    reader.readAsText(file);
  };

  const handleManualDataChange = (e) => {
    setManualData(e.target.value);
  };

  const parseManualData = () => {
    if (!manualData.trim()) {
      toast.error('Please enter data');
      return;
    }

    try {
      const lines = manualData.trim().split('\n');
      const data = [];
      
      for (const line of lines) {
        const parts = line.split('\t').map(p => p.trim()); // Tab-separated
        if (parts.length >= 3) {
          data.push({
            studentRoll: parts[0],
            studentName: parts[1],
            obtained: parseFloat(parts[2]) || 0,
            status: parts[3] || 'Pass',
            grade: parts[4] || '',
            remarks: parts[5] || '',
            evaluationDate: parts[6] || new Date().toISOString().split('T')[0],
          });
        }
      }

      if (data.length === 0) {
        toast.error('No valid data found. Please check the format.');
        return;
      }

      setPreviewData(data);
      setShowPreview(true);
    } catch (error) {
      toast.error('Error parsing data. Please check the format.');
    }
  };

  const downloadTemplate = () => {
    const assessment = assessments.find(a => a._id === selectedAssessment);
    if (!assessment) {
      toast.error('Please select an assessment first');
      return;
    }

    let csvContent = 'studentRoll,studentName,obtained,status,grade,remarks,evaluationDate\n';
    
    // Add sample rows
    csvContent += 'ROLL001,John Doe,85,Pass,A,,2024-01-15\n';
    csvContent += 'ROLL002,Jane Smith,92,Pass,A+,,2024-01-15\n';
    csvContent += 'ROLL003,Bob Johnson,65,Pass,B,,2024-01-15\n';

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${assessment.title}_template.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedAssessment) {
      toast.error('Please select an assessment');
      return;
    }

    if (previewData.length === 0) {
      toast.error('No data to upload');
      return;
    }

    const assessment = assessments.find(a => a._id === selectedAssessment);
    
    // Validate marks against max marks
    const invalidMarks = previewData.filter(row => 
      row.obtained < 0 || row.obtained > assessment.maxMarks
    );
    
    if (invalidMarks.length > 0) {
      toast.error(`Invalid marks found. Marks should be between 0 and ${assessment.maxMarks}`);
      return;
    }

    setLoading(true);
    try {
      await onSubmit({
        assessmentId: selectedAssessment,
        results: previewData,
      });
    } catch (error) {
      console.error('Error uploading results:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectedAssessmentObj = assessments.find(a => a._id === selectedAssessment);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Bulk Upload Results
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Assessment Selection */}
                  <div>
                    <label htmlFor="assessment" className="block text-sm font-medium text-gray-700">
                      Select Assessment *
                    </label>
                    <select
                      id="assessment"
                      value={selectedAssessment}
                      onChange={handleAssessmentChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Choose an assessment</option>
                      {assessments.map((assessment) => (
                        <option key={assessment._id} value={assessment._id}>
                          {assessment.title} ({assessment.type}) - {assessment.course?.code} - Max: {assessment.maxMarks}
                        </option>
                      ))}
                    </select>
                  </div>

                  {selectedAssessmentObj && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <h4 className="text-sm font-medium text-blue-900 mb-2">Assessment Details</h4>
                      <div className="grid grid-cols-1 gap-2 sm:grid-cols-3 text-sm text-blue-800">
                        <div>Max Marks: {selectedAssessmentObj.maxMarks}</div>
                        <div>Weightage: {selectedAssessmentObj.weightage}%</div>
                        <div>Course: {selectedAssessmentObj.course?.code}</div>
                      </div>
                    </div>
                  )}

                  {/* Upload Mode Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Upload Method
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="csv"
                          checked={uploadMode === 'csv'}
                          onChange={(e) => setUploadMode(e.target.value)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">CSV File Upload</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="manual"
                          checked={uploadMode === 'manual'}
                          onChange={(e) => setUploadMode(e.target.value)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">Manual Entry</span>
                      </label>
                    </div>
                  </div>

                  {/* CSV Upload */}
                  {uploadMode === 'csv' && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label className="block text-sm font-medium text-gray-700">
                          Upload CSV File
                        </label>
                        <button
                          type="button"
                          onClick={downloadTemplate}
                          disabled={!selectedAssessment}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                          <DocumentArrowDownIcon className="-ml-0.5 mr-2 h-4 w-4" />
                          Download Template
                        </button>
                      </div>
                      
                      <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div className="space-y-1 text-center">
                          <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <div className="flex text-sm text-gray-600">
                            <label
                              htmlFor="file-upload"
                              className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                            >
                              <span>Upload a file</span>
                              <input
                                id="file-upload"
                                ref={fileInputRef}
                                name="file-upload"
                                type="file"
                                accept=".csv"
                                onChange={handleFileChange}
                                className="sr-only"
                              />
                            </label>
                            <p className="pl-1">or drag and drop</p>
                          </div>
                          <p className="text-xs text-gray-500">CSV files only</p>
                        </div>
                      </div>
                      
                      {file && (
                        <div className="text-sm text-gray-600">
                          Selected file: {file.name}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Manual Entry */}
                  {uploadMode === 'manual' && (
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="manual-data" className="block text-sm font-medium text-gray-700">
                          Enter Data (Tab-separated)
                        </label>
                        <div className="mt-1 text-xs text-gray-500 mb-2">
                          Format: Roll Number [TAB] Student Name [TAB] Obtained Marks [TAB] Status [TAB] Grade [TAB] Remarks [TAB] Date
                        </div>
                        <textarea
                          id="manual-data"
                          rows={8}
                          value={manualData}
                          onChange={handleManualDataChange}
                          placeholder="ROLL001	John Doe	85	Pass	A		2024-01-15"
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-mono"
                        />
                      </div>
                      
                      <button
                        type="button"
                        onClick={parseManualData}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Parse Data
                      </button>
                    </div>
                  )}

                  {/* Data Preview */}
                  {showPreview && previewData.length > 0 && (
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Data Preview ({previewData.length} records)
                      </h4>
                      <div className="overflow-x-auto max-h-64 border border-gray-200 rounded-md">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Roll Number
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Student Name
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Obtained
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Grade
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {previewData.slice(0, 10).map((row, index) => (
                              <tr key={index}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {row.studentRoll}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {row.studentName}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {row.obtained}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    row.status === 'Pass' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {row.status}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {row.grade}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      {previewData.length > 10 && (
                        <p className="text-sm text-gray-500 mt-2">
                          Showing first 10 records. Total: {previewData.length}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading || previewData.length === 0}
                      className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Uploading...' : `Upload ${previewData.length} Results`}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
