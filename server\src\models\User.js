const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address',
    ],
  },
  passwordHash: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false, // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: {
      values: ['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'],
      message: 'Role must be one of: ADMIN, DEPT_HEAD, FACULTY, AUDITOR',
    },
    default: 'FACULTY',
  },
  deptId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department',
    required: function() {
      return this.role !== 'ADMIN'; // Admin doesn't need department
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  lastLogin: {
    type: Date,
  },
  refreshToken: {
    type: String,
    select: false,
  },
  profilePicture: {
    type: String,
  },
  phone: {
    type: String,
    trim: true,
  },
  designation: {
    type: String,
    trim: true,
  },
  qualifications: [{
    degree: String,
    institution: String,
    year: Number,
  }],
  experience: {
    type: Number, // Years of experience
    min: 0,
  },
  specializations: [String],
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes (email index is automatically created by unique: true)
UserSchema.index({ role: 1 });
UserSchema.index({ deptId: 1 });
UserSchema.index({ isActive: 1 });

// Virtual for department details
UserSchema.virtual('department', {
  ref: 'Department',
  localField: 'deptId',
  foreignField: '_id',
  justOne: true,
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('passwordHash')) return next();

  try {
    // Hash the password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.passwordHash = await bcrypt.hash(this.passwordHash, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.passwordHash);
};

// Instance method to generate full name
UserSchema.methods.getFullName = function() {
  return this.name;
};

// Instance method to check if user has permission
UserSchema.methods.hasPermission = function(requiredRole) {
  const roleHierarchy = {
    'AUDITOR': 1,
    'FACULTY': 2,
    'DEPT_HEAD': 3,
    'ADMIN': 4,
  };

  return roleHierarchy[this.role] >= roleHierarchy[requiredRole];
};

// Static method to find by email
UserSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to get active users by department
UserSchema.statics.getActiveByDepartment = function(deptId) {
  return this.find({ deptId, isActive: true }).populate('department');
};

// Static method to get users by role
UserSchema.statics.getByRole = function(role) {
  return this.find({ role, isActive: true }).populate('department');
};

module.exports = mongoose.model('User', UserSchema);
