const express = require('express');
const {
  getAssessments,
  getAssessmentById,
  createAssessment,
  updateAssessment,
  updateAssessmentStatus,
  deleteAssessment,
  getAssessmentsByCourse,
  createAssessmentValidation,
  updateAssessmentValidation,
  handleValidationErrors,
} = require('../controllers/assessmentController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all assessments (with filtering and pagination)
router.get('/', getAssessments);

// Get assessments by course
router.get('/course/:courseId', getAssessmentsByCourse);

// Get assessment by ID
router.get('/:id', getAssessmentById);

// Create new assessment (Admin, Dept Head, and assigned Faculty)
router.post('/', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), createAssessmentValidation, handleValidationErrors, createAssessment);

// Update assessment (Admin, Dept Head, and assigned Faculty)
router.put('/:id', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), updateAssessmentValidation, handleValidationErrors, updateAssessment);

// Update assessment status (Admin, Dept Head, and assigned Faculty)
router.patch('/:id/status', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), updateAssessmentStatus);

// Delete assessment (Admin, Dept Head, and assigned Faculty)
router.delete('/:id', authorize('ADMIN', 'DEPT_HEAD', 'FACULTY'), deleteAssessment);

module.exports = router;
