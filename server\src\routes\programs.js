const express = require('express');
const {
  getPrograms,
  getProgramById,
  createProgram,
  updateProgram,
  updatePOs,
  updatePSOs,
  getProgramCourses,
  deactivateProgram,
  activateProgram,
  createProgramValidation,
  updateProgramValidation,
  handleValidationErrors,
} = require('../controllers/programController');
const { authenticate, authorize, authorizeDepartment } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all programs (with filtering and pagination)
router.get('/', getPrograms);

// Get program by ID
router.get('/:id', getProgramById);

// Get program courses
router.get('/:id/courses', getProgramCourses);

// Create new program (Admin and Dept Head only)
router.post('/', authorize('ADMIN', 'DEPT_HEAD'), createProgramValidation, handleValidationErrors, createProgram);

// Update program (Admin and Dept Head only)
router.put('/:id', authorize('ADMIN', 'DEPT_HEAD'), updateProgramValidation, handleValidationErrors, updateProgram);

// Update Program Outcomes (Admin and Dept Head only)
router.put('/:id/pos', authorize('ADMIN', 'DEPT_HEAD'), updatePOs);

// Update Program Specific Outcomes (Admin and Dept Head only)
router.put('/:id/psos', authorize('ADMIN', 'DEPT_HEAD'), updatePSOs);

// Deactivate program (Admin only)
router.put('/:id/deactivate', authorize('ADMIN'), deactivateProgram);

// Activate program (Admin only)
router.put('/:id/activate', authorize('ADMIN'), activateProgram);

module.exports = router;
