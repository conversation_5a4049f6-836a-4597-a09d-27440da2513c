// Standardized API response utility

const sendResponse = (res, statusCode, success, message, data = null, meta = null) => {
  const response = {
    success,
    message,
    timestamp: new Date().toISOString(),
  };

  if (data !== null) {
    response.data = data;
  }

  if (meta !== null) {
    response.meta = meta;
  }

  return res.status(statusCode).json(response);
};

// Success responses
const sendSuccess = (res, message, data = null, meta = null) => {
  return sendResponse(res, 200, true, message, data, meta);
};

const sendCreated = (res, message, data = null) => {
  return sendResponse(res, 201, true, message, data);
};

const sendNoContent = (res, message = 'Operation completed successfully') => {
  return sendResponse(res, 204, true, message);
};

// Error responses
const sendError = (res, statusCode, message, errors = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString(),
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

const sendBadRequest = (res, message, errors = null) => {
  return sendError(res, 400, message, errors);
};

const sendUnauthorized = (res, message = 'Unauthorized access') => {
  return sendError(res, 401, message);
};

const sendForbidden = (res, message = 'Access forbidden') => {
  return sendError(res, 403, message);
};

const sendNotFound = (res, message = 'Resource not found') => {
  return sendError(res, 404, message);
};

const sendConflict = (res, message = 'Resource conflict') => {
  return sendError(res, 409, message);
};

const sendInternalError = (res, message = 'Internal server error') => {
  return sendError(res, 500, message);
};

// Pagination helper
const getPaginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: total,
      itemsPerPage: limit,
      hasNextPage,
      hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null,
    },
  };
};

module.exports = {
  sendResponse,
  sendSuccess,
  sendCreated,
  sendNoContent,
  sendError,
  sendBadRequest,
  sendUnauthorized,
  sendForbidden,
  sendNotFound,
  sendConflict,
  sendInternalError,
  getPaginationMeta,
};
