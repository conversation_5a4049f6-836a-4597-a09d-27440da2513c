import { useState } from 'react';
import {
  ClipboardDocumentListIcon,
  CalendarIcon,
  ClockIcon,
  AcademicCapIcon,
  PencilIcon,
  CogIcon,
  TrashIcon,
  EllipsisVerticalIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const AssessmentCard = ({ 
  assessment, 
  onEdit, 
  onManageQuestions, 
  onStatusChange, 
  onDelete 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getTypeColor = (type) => {
    const colors = {
      'T1': 'bg-blue-100 text-blue-800',
      'T2': 'bg-green-100 text-green-800',
      'EndSem': 'bg-red-100 text-red-800',
      'Quiz': 'bg-yellow-100 text-yellow-800',
      'Assignment': 'bg-purple-100 text-purple-800',
      'Lab': 'bg-indigo-100 text-indigo-800',
      'Project': 'bg-pink-100 text-pink-800',
      'Presentation': 'bg-orange-100 text-orange-800',
      'Viva': 'bg-gray-100 text-gray-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status) => {
    const colors = {
      'Draft': 'bg-gray-100 text-gray-800',
      'Published': 'bg-blue-100 text-blue-800',
      'Ongoing': 'bg-yellow-100 text-yellow-800',
      'Completed': 'bg-green-100 text-green-800',
      'Cancelled': 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed':
        return CheckCircleIcon;
      case 'Cancelled':
        return XCircleIcon;
      case 'Ongoing':
        return PlayIcon;
      case 'Published':
        return PauseIcon;
      default:
        return ClipboardDocumentListIcon;
    }
  };

  const isUpcoming = new Date(assessment.date) > new Date();
  const isPast = new Date(assessment.date) < new Date();
  const canEdit = assessment.status === 'Draft' || assessment.status === 'Published';
  const canDelete = assessment.status === 'Draft';

  const StatusIcon = getStatusIcon(assessment.status);

  const getAvailableStatusTransitions = (currentStatus) => {
    const transitions = {
      'Draft': ['Published', 'Cancelled'],
      'Published': ['Ongoing', 'Cancelled'],
      'Ongoing': ['Completed', 'Cancelled'],
      'Completed': [],
      'Cancelled': ['Draft'],
    };
    return transitions[currentStatus] || [];
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(assessment.type)}`}>
                {assessment.type}
              </span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {assessment.status}
              </span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 truncate" title={assessment.title}>
              {assessment.title}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {assessment.course?.code} - {assessment.course?.title}
            </p>
          </div>
          
          {/* Actions Menu */}
          <Menu as="div" className="relative inline-block text-left">
            <div>
              <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1">
                <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
              </Menu.Button>
            </div>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <div className="py-1">
                  {canEdit && (
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={() => onEdit(assessment)}
                          className={classNames(
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center px-4 py-2 text-sm w-full text-left'
                          )}
                        >
                          <PencilIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                          Edit Assessment
                        </button>
                      )}
                    </Menu.Item>
                  )}
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onManageQuestions(assessment)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <CogIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        Manage Questions
                      </button>
                    )}
                  </Menu.Item>
                  
                  {/* Status Change Options */}
                  {getAvailableStatusTransitions(assessment.status).map((status) => (
                    <Menu.Item key={status}>
                      {({ active }) => (
                        <button
                          onClick={() => onStatusChange(assessment._id, status)}
                          className={classNames(
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center px-4 py-2 text-sm w-full text-left'
                          )}
                        >
                          <StatusIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                          Mark as {status}
                        </button>
                      )}
                    </Menu.Item>
                  ))}

                  {canDelete && (
                    <>
                      <div className="border-t border-gray-100" />
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={() => onDelete(assessment._id)}
                            className={classNames(
                              active ? 'bg-red-50 text-red-900' : 'text-red-700',
                              'group flex items-center px-4 py-2 text-sm w-full text-left'
                            )}
                          >
                            <TrashIcon className="mr-3 h-4 w-4 text-red-400 group-hover:text-red-500" aria-hidden="true" />
                            Delete Assessment
                          </button>
                        )}
                      </Menu.Item>
                    </>
                  )}
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        {/* Assessment Details */}
        <div className="mt-4 space-y-3">
          {/* Date and Duration */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-gray-400" />
              <span className={`text-sm ${isUpcoming ? 'text-blue-600' : isPast ? 'text-gray-500' : 'text-green-600'}`}>
                {new Date(assessment.date).toLocaleDateString('en-US', {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <ClockIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {assessment.duration} min
              </span>
            </div>
          </div>

          {/* Marks and Weightage */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AcademicCapIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {assessment.maxMarks} marks
              </span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {assessment.weightage}% weightage
            </span>
          </div>

          {/* CO Tags */}
          {assessment.coTags && assessment.coTags.length > 0 && (
            <div>
              <div className="flex items-center space-x-1 mb-1">
                <span className="text-xs text-gray-500">Course Outcomes:</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {assessment.coTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    CO{tag.coIndex} ({tag.marks}m)
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Questions Count */}
          {assessment.questions && assessment.questions.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Questions:</span>
              <span className="text-sm font-medium text-gray-900">
                {assessment.questions.length}
              </span>
            </div>
          )}
        </div>

        {/* Expandable Description */}
        {assessment.description && (
          <div className="mt-4">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none"
            >
              {isExpanded ? 'Show less' : 'Show description'}
            </button>
            {isExpanded && (
              <p className="mt-2 text-sm text-gray-600 leading-relaxed">
                {assessment.description}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">
            Created {new Date(assessment.createdAt).toLocaleDateString()}
          </span>
          <div className="flex space-x-2">
            <button
              onClick={() => onManageQuestions(assessment)}
              className="text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              Questions ({assessment.questions?.length || 0})
            </button>
            {canEdit && (
              <>
                <span className="text-gray-300">•</span>
                <button
                  onClick={() => onEdit(assessment)}
                  className="text-xs text-blue-600 hover:text-blue-500 font-medium"
                >
                  Edit
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
