const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import models
const User = require('../src/models/User');
const Department = require('../src/models/Department');

const seedAdmin = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('ℹ️  Admin user already exists');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
      process.exit(0);
    }

    // Create a default department for testing
    let department = await Department.findOne({ code: 'CSE' });
    if (!department) {
      department = new Department({
        name: 'Computer Science and Engineering',
        code: 'CSE',
        description: 'Department of Computer Science and Engineering',
        establishedYear: 2000,
        contactInfo: {
          email: '<EMAIL>',
          phone: '******-567-8900',
          address: 'University Campus, Building A'
        },
        isActive: true
      });
      await department.save();
      console.log('✅ Created default CSE department');
    }

    // Create admin user
    const adminUser = new User({
      name: 'System Administrator',
      email: '<EMAIL>',
      passwordHash: 'admin123', // Will be hashed by pre-save middleware
      role: 'ADMIN',
      phone: '******-567-8901',
      designation: 'System Administrator',
      isActive: true,
      // Admin doesn't need department
    });

    await adminUser.save();
    console.log('✅ Created admin user successfully!');
    console.log('');
    console.log('🎉 Database seeded successfully!');
    console.log('');
    console.log('📋 Login Credentials:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: ADMIN');
    console.log('');
    console.log('🌐 You can now login to the application!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the seeding
seedAdmin();
