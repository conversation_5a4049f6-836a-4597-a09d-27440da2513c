const mongoose = require('mongoose');

const IndirectSurveySchema = new mongoose.Schema({
  courseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, 'Course is required'],
  },
  title: {
    type: String,
    required: [true, 'Survey title is required'],
    trim: true,
    maxlength: [200, 'Survey title cannot exceed 200 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Survey description cannot exceed 1000 characters'],
  },
  respondentType: {
    type: String,
    enum: {
      values: ['Student', 'Alumni', 'Employer', 'Industry Expert', 'Faculty Peer'],
      message: 'Respondent type must be one of: Student, Alumni, Employer, Industry Expert, Faculty Peer',
    },
    required: [true, 'Respondent type is required'],
  },
  academicYear: {
    type: String,
    required: [true, 'Academic year is required'],
    match: [/^\d{4}-\d{4}$/, 'Academic year format should be YYYY-YYYY (e.g., 2023-2024)'],
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
    min: [1, 'Semester must be at least 1'],
    max: [20, 'Semester cannot exceed 20'],
  },
  // Survey configuration
  surveyPeriod: {
    startDate: {
      type: Date,
      required: [true, 'Survey start date is required'],
    },
    endDate: {
      type: Date,
      required: [true, 'Survey end date is required'],
      validate: {
        validator: function(value) {
          return value > this.surveyPeriod.startDate;
        },
        message: 'End date must be after start date',
      },
    },
  },
  isAnonymous: {
    type: Boolean,
    default: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // CO-wise questions and responses
  responses: [{
    coIndex: {
      type: Number,
      required: true,
      min: [1, 'CO index must be at least 1'],
    },
    question: {
      type: String,
      required: true,
      trim: true,
      maxlength: [500, 'Question cannot exceed 500 characters'],
    },
    responseType: {
      type: String,
      enum: ['Likert5', 'Likert7', 'Rating10', 'YesNo', 'Text'],
      default: 'Likert5',
    },
    // Individual responses
    individualResponses: [{
      respondentId: {
        type: String, // Can be anonymous ID or actual user ID
        required: true,
      },
      response: {
        type: mongoose.Schema.Types.Mixed, // Can be number, string, or boolean
        required: true,
      },
      submittedAt: {
        type: Date,
        default: Date.now,
      },
      comments: {
        type: String,
        trim: true,
        maxlength: [1000, 'Comments cannot exceed 1000 characters'],
      },
    }],
    // Aggregated statistics
    statistics: {
      totalResponses: {
        type: Number,
        default: 0,
      },
      averageScore: {
        type: Number,
        default: 0,
        min: [0, 'Average score cannot be negative'],
      },
      normalizedScore: {
        type: Number,
        default: 0,
        min: [0, 'Normalized score cannot be negative'],
        max: [1, 'Normalized score cannot exceed 1'],
      },
      distribution: {
        type: Map,
        of: Number,
        default: new Map(),
      },
    },
  }],
  // Overall survey statistics
  overallStatistics: {
    totalInvited: {
      type: Number,
      default: 0,
      min: [0, 'Total invited cannot be negative'],
    },
    totalResponses: {
      type: Number,
      default: 0,
      min: [0, 'Total responses cannot be negative'],
    },
    responseRate: {
      type: Number,
      default: 0,
      min: [0, 'Response rate cannot be negative'],
      max: [100, 'Response rate cannot exceed 100'],
    },
    averageCompletionTime: {
      type: Number, // in minutes
      default: 0,
      min: [0, 'Average completion time cannot be negative'],
    },
    satisfactionScore: {
      type: Number,
      default: 0,
      min: [0, 'Satisfaction score cannot be negative'],
      max: [10, 'Satisfaction score cannot exceed 10'],
    },
  },
  // Survey metadata
  surveyMethod: {
    type: String,
    enum: ['Online', 'Paper', 'Interview', 'Phone', 'Mixed'],
    default: 'Online',
  },
  platform: {
    type: String,
    trim: true,
    maxlength: [100, 'Platform name cannot exceed 100 characters'],
  },
  // Quality assurance
  validationRules: [{
    field: String,
    rule: String,
    message: String,
  }],
  qualityChecks: {
    duplicateResponses: {
      type: Number,
      default: 0,
    },
    incompleteResponses: {
      type: Number,
      default: 0,
    },
    suspiciousResponses: {
      type: Number,
      default: 0,
    },
  },
  // Status tracking
  status: {
    type: String,
    enum: ['Draft', 'Active', 'Closed', 'Analyzed', 'Archived'],
    default: 'Draft',
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  analyzedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  analyzedAt: {
    type: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
IndirectSurveySchema.index({ courseId: 1, respondentType: 1 });
IndirectSurveySchema.index({ academicYear: 1, semester: 1 });
IndirectSurveySchema.index({ status: 1 });
IndirectSurveySchema.index({ 'surveyPeriod.startDate': 1, 'surveyPeriod.endDate': 1 });
IndirectSurveySchema.index({ isActive: 1 });

// Virtual for course details
IndirectSurveySchema.virtual('course', {
  ref: 'Course',
  localField: 'courseId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for survey duration
IndirectSurveySchema.virtual('surveyDuration').get(function() {
  if (this.surveyPeriod.startDate && this.surveyPeriod.endDate) {
    const diffTime = Math.abs(this.surveyPeriod.endDate - this.surveyPeriod.startDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // days
  }
  return 0;
});

// Pre-save middleware to calculate statistics
IndirectSurveySchema.pre('save', function(next) {
  // Calculate response statistics for each CO
  this.responses.forEach(response => {
    const totalResponses = response.individualResponses.length;
    response.statistics.totalResponses = totalResponses;

    if (totalResponses > 0) {
      // Calculate average score based on response type
      let sum = 0;
      let maxPossible = 5; // default for Likert5

      response.individualResponses.forEach(indResponse => {
        if (typeof indResponse.response === 'number') {
          sum += indResponse.response;
        } else if (typeof indResponse.response === 'boolean') {
          sum += indResponse.response ? 1 : 0;
          maxPossible = 1;
        }
      });

      // Set max possible based on response type
      switch (response.responseType) {
        case 'Likert7':
          maxPossible = 7;
          break;
        case 'Rating10':
          maxPossible = 10;
          break;
        case 'YesNo':
          maxPossible = 1;
          break;
      }

      response.statistics.averageScore = sum / totalResponses;
      response.statistics.normalizedScore = (sum / totalResponses) / maxPossible;
    }
  });

  // Calculate overall statistics
  const totalResponses = this.responses.reduce((sum, r) => sum + r.statistics.totalResponses, 0);
  this.overallStatistics.totalResponses = totalResponses;

  if (this.overallStatistics.totalInvited > 0) {
    this.overallStatistics.responseRate = (totalResponses / this.overallStatistics.totalInvited) * 100;
  }

  next();
});

// Static method to get surveys by course
IndirectSurveySchema.statics.getByCourse = function(courseId, respondentType = null) {
  const filter = { courseId, isActive: true };
  if (respondentType) filter.respondentType = respondentType;

  return this.find(filter)
    .populate('course', 'title code semester year')
    .sort({ createdAt: -1 });
};

// Static method to get surveys by academic year
IndirectSurveySchema.statics.getByAcademicYear = function(academicYear, programId = null) {
  const pipeline = [
    { $match: { academicYear, isActive: true } },
    {
      $lookup: {
        from: 'courses',
        localField: 'courseId',
        foreignField: '_id',
        as: 'course'
      }
    },
    { $unwind: '$course' },
  ];

  if (programId) {
    pipeline.push({ $match: { 'course.programId': mongoose.Types.ObjectId(programId) } });
  }

  return this.aggregate(pipeline);
};

// Instance method to add response
IndirectSurveySchema.methods.addResponse = function(coIndex, respondentId, response, comments = '') {
  const coResponse = this.responses.find(r => r.coIndex === coIndex);
  
  if (!coResponse) {
    throw new Error(`CO${coIndex} not found in survey`);
  }

  // Check for duplicate response from same respondent
  const existingResponse = coResponse.individualResponses.find(r => r.respondentId === respondentId);
  if (existingResponse) {
    throw new Error('Response already exists for this respondent and CO');
  }

  coResponse.individualResponses.push({
    respondentId,
    response,
    comments,
    submittedAt: new Date(),
  });

  return this.save();
};

// Instance method to calculate CO attainment from survey
IndirectSurveySchema.methods.calculateCOAttainment = function() {
  const coAttainment = {};

  this.responses.forEach(response => {
    coAttainment[`CO${response.coIndex}`] = {
      normalizedScore: response.statistics.normalizedScore,
      averageScore: response.statistics.averageScore,
      totalResponses: response.statistics.totalResponses,
      attainmentLevel: this.getAttainmentLevel(response.statistics.normalizedScore),
    };
  });

  return coAttainment;
};

// Instance method to get attainment level
IndirectSurveySchema.methods.getAttainmentLevel = function(normalizedScore) {
  if (normalizedScore >= 0.8) return 'High';
  if (normalizedScore >= 0.6) return 'Medium';
  if (normalizedScore >= 0.4) return 'Low';
  return 'Very Low';
};

// Instance method to close survey and analyze
IndirectSurveySchema.methods.closeSurvey = function(userId) {
  this.status = 'Closed';
  this.analyzedBy = userId;
  this.analyzedAt = new Date();
  this.updatedBy = userId;
  
  return this.save();
};

module.exports = mongoose.model('IndirectSurvey', IndirectSurveySchema);
