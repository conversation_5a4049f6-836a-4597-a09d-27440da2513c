import axios from 'axios';
import { useAuthStore } from '../stores/authStore';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token;
    console.log('API Interceptor: Adding token to request:', token ? 'Token present' : 'No token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const { refreshAuth } = useAuthStore.getState();
        const result = await refreshAuth();
        
        if (result.success) {
          // Retry the original request with new token
          const newToken = useAuthStore.getState().token;
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        } else {
          // Refresh failed, redirect to login
          useAuthStore.getState().logout();
          window.location.href = '/login';
        }
      } catch (refreshError) {
        useAuthStore.getState().logout();
        window.location.href = '/login';
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.status === 403) {
      toast.error('Access denied. You do not have permission to perform this action.');
    } else if (error.response?.status === 404) {
      toast.error('Resource not found.');
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refreshToken: (data) => api.post('/auth/refresh', data),
  updateProfile: (data) => api.put('/auth/profile', data),
  changePassword: (data) => api.put('/auth/change-password', data),
  getProfile: () => api.get('/auth/profile'),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUserById: (id) => api.get(`/users/${id}`),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deactivateUser: (id) => api.put(`/users/${id}/deactivate`),
  activateUser: (id) => api.put(`/users/${id}/activate`),
  getUsersByRole: (role) => api.get(`/users/role/${role}`),
  getDepartmentStats: (deptId) => api.get(`/users/department/${deptId}/stats`),
};

// Departments API
export const departmentsAPI = {
  getDepartments: (params) => api.get('/departments', { params }),
  getDepartmentById: (id) => api.get(`/departments/${id}`),
  createDepartment: (data) => api.post('/departments', data),
  updateDepartment: (id, data) => api.put(`/departments/${id}`, data),
  deactivateDepartment: (id) => api.put(`/departments/${id}/deactivate`),
  activateDepartment: (id) => api.put(`/departments/${id}/activate`),
  getDepartmentFaculty: (id) => api.get(`/departments/${id}/faculty`),
};

// Programs API
export const programsAPI = {
  getPrograms: (params) => api.get('/programs', { params }),
  getProgramById: (id) => api.get(`/programs/${id}`),
  createProgram: (data) => api.post('/programs', data),
  updateProgram: (id, data) => api.put(`/programs/${id}`, data),
  updatePOs: (id, data) => api.put(`/programs/${id}/pos`, data),
  updatePSOs: (id, data) => api.put(`/programs/${id}/psos`, data),
  getProgramCourses: (id, params) => api.get(`/programs/${id}/courses`, { params }),
  deactivateProgram: (id) => api.put(`/programs/${id}/deactivate`),
  activateProgram: (id) => api.put(`/programs/${id}/activate`),
};

// Courses API
export const coursesAPI = {
  getCourses: (params) => api.get('/courses', { params }),
  getCourseById: (id) => api.get(`/courses/${id}`),
  createCourse: (data) => api.post('/courses', data),
  updateCourse: (id, data) => api.put(`/courses/${id}`, data),
  updateCOs: (id, data) => api.put(`/courses/${id}/cos`, data),
  updateMapping: (id, data) => api.put(`/courses/${id}/mapping`, data),
};

// OBE Config API
export const obeConfigAPI = {
  getAllConfigs: (params) => api.get('/obe-config', { params }),
  getConfigByProgram: (programId) => api.get(`/obe-config/program/${programId}`),
  createOrUpdateConfig: (programId, data) => api.post(`/obe-config/program/${programId}`, data),
  updateAttainmentBands: (programId, data) => api.put(`/obe-config/program/${programId}/attainment-bands`, data),
  updateAssessmentWeights: (programId, data) => api.put(`/obe-config/program/${programId}/assessment-weights`, data),
  updateTargets: (programId, data) => api.put(`/obe-config/program/${programId}/targets`, data),
  resetToDefault: (programId) => api.post(`/obe-config/program/${programId}/reset`),
  deleteConfig: (programId) => api.delete(`/obe-config/program/${programId}`),
};

// Assessments API
export const assessmentsAPI = {
  getAssessments: (params) => api.get('/assessments', { params }),
  getAssessmentById: (id) => api.get(`/assessments/${id}`),
  createAssessment: (data) => api.post('/assessments', data),
  updateAssessment: (id, data) => api.put(`/assessments/${id}`, data),
  updateAssessmentStatus: (id, data) => api.patch(`/assessments/${id}/status`, data),
  deleteAssessment: (id) => api.delete(`/assessments/${id}`),
  getAssessmentsByCourse: (courseId, params) => api.get(`/assessments/course/${courseId}`, { params }),
};

// Results API
export const resultsAPI = {
  getResults: (params) => api.get('/results', { params }),
  getResultById: (id) => api.get(`/results/${id}`),
  createResult: (data) => api.post('/results', data),
  bulkUploadResults: (formData) => api.post('/results/bulk', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
};

// Attainment API
export const attainmentAPI = {
  getAttainmentSummary: (params) => api.get('/attainment/summary', { params }),
  getAttainmentTrends: (params) => api.get('/attainment/trends', { params }),
  getCourseAttainment: (courseId, params) => api.get(`/attainment/course/${courseId}`, { params }),
  getProgramAttainment: (programId, params) => api.get(`/attainment/program/${programId}`, { params }),
  getCOPOAnalysis: (courseId) => api.get(`/attainment/course/${courseId}/co-po-analysis`),
};

// Evidence API
export const evidenceAPI = {
  getEvidence: (params) => api.get('/evidence', { params }),
  getEvidenceById: (id) => api.get(`/evidence/${id}`),
  createEvidence: (formData) => api.post('/evidence', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateEvidence: (id, data) => api.put(`/evidence/${id}`, data),
  verifyEvidence: (id, data) => api.patch(`/evidence/${id}/verify`, data),
  deleteEvidence: (id) => api.delete(`/evidence/${id}`),
  downloadEvidence: (id) => api.get(`/evidence/${id}/download`, {
    responseType: 'blob',
  }),
  getEvidenceByCriterion: (criterion, params) => api.get(`/evidence/criterion/${criterion}`, { params }),
};

// Readiness API
export const readinessAPI = {
  getProgramReadiness: (programId, params) => api.get(`/readiness/program/${programId}`, { params }),
  getReadinessDashboard: (programId, params) => api.get(`/readiness/program/${programId}/dashboard`, { params }),
  getReadinessTrends: (programId, params) => api.get(`/readiness/program/${programId}/trends`, { params }),
  getReadinessMetrics: (programId, params) => api.get(`/readiness/program/${programId}/metrics`, { params }),
  getReadinessMetricById: (id) => api.get(`/readiness/metric/${id}`),
  createReadinessMetric: (data) => api.post('/readiness/metric', data),
  updateReadinessMetric: (id, data) => api.put(`/readiness/metric/${id}`, data),
  updateMetricValue: (id, data) => api.patch(`/readiness/metric/${id}/value`, data),
  addActionItem: (id, data) => api.post(`/readiness/metric/${id}/action-item`, data),
};

// Reports API
export const reportsAPI = {
  generateSAR: (programId, params) => api.get(`/reports/sar/${programId}`, { 
    params,
    responseType: 'blob',
  }),
  getReportsList: (params) => api.get('/reports', { params }),
  getReportById: (id) => api.get(`/reports/${id}`),
  deleteReport: (id) => api.delete(`/reports/${id}`),
};

// Utility functions
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default api;
