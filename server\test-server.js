const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Server is running!',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Test auth route
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Mock authentication for testing
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      data: {
        user: {
          id: '1',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'ADMIN'
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ Test server running on port ${PORT}`);
  console.log(`🌐 Frontend should connect to: http://localhost:${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
});
