const { body } = require('express-validator');
const User = require('../models/User');
const Department = require('../models/Department');
const { generateTokens, verifyToken } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendUnauthorized } = require('../utils/response');
const logger = require('../utils/logger');

// Validation rules
const registerValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('role')
    .isIn(['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'])
    .withMessage('Invalid role specified'),
  body('deptId')
    .optional()
    .isMongoId()
    .withMessage('Invalid department ID'),
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

// Register new user (Admin only)
const register = async (req, res, next) => {
  try {
    const { name, email, password, role, deptId, phone, designation } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return next(new AppError('User with this email already exists', 409));
    }

    // Validate department for non-admin users
    if (role !== 'ADMIN' && deptId) {
      const department = await Department.findById(deptId);
      if (!department || !department.isActive) {
        return next(new AppError('Invalid or inactive department', 400));
      }
    }

    // Create new user
    const userData = {
      name,
      email,
      passwordHash: password, // Will be hashed by pre-save middleware
      role,
      phone,
      designation,
      createdBy: req.user._id,
    };

    // Add department for non-admin users
    if (role !== 'ADMIN' && deptId) {
      userData.deptId = deptId;
    }

    const user = new User(userData);
    await user.save();

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.passwordHash;

    logger.info(`New user registered: ${email} by ${req.user.email}`);

    return sendCreated(res, 'User registered successfully', userResponse);
  } catch (error) {
    return next(error);
  }
};

// User login
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user and include password for comparison
    const user = await User.findByEmail(email).select('+passwordHash').populate('department');
    
    if (!user || !user.isActive) {
      return sendUnauthorized(res, 'Invalid credentials or account deactivated');
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return sendUnauthorized(res, 'Invalid credentials');
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id);

    // Update user's refresh token and last login
    user.refreshToken = refreshToken;
    user.lastLogin = new Date();
    await user.save();

    // Remove sensitive data from response
    const userResponse = user.toObject();
    delete userResponse.passwordHash;
    delete userResponse.refreshToken;

    logger.info(`User logged in: ${email}`);

    return sendSuccess(res, 'Login successful', {
      user: userResponse,
      token: accessToken,
      refreshToken,
    });
  } catch (error) {
    return next(error);
  }
};

// Refresh access token
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return next(new AppError('Refresh token is required', 400));
    }

    // Verify refresh token
    const decoded = await verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Find user and check if refresh token matches
    const user = await User.findById(decoded.userId).select('+refreshToken');
    
    if (!user || !user.isActive || user.refreshToken !== refreshToken) {
      return sendUnauthorized(res, 'Invalid refresh token');
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(user._id);

    // Update user's refresh token
    user.refreshToken = newRefreshToken;
    await user.save();

    return sendSuccess(res, 'Token refreshed successfully', {
      tokens: {
        accessToken,
        refreshToken: newRefreshToken,
      },
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return sendUnauthorized(res, 'Invalid refresh token');
    }
    return next(error);
  }
};

// User logout
const logout = async (req, res, next) => {
  try {
    // Clear refresh token
    await User.findByIdAndUpdate(req.user._id, { refreshToken: null });

    logger.info(`User logged out: ${req.user.email}`);

    return sendSuccess(res, 'Logout successful');
  } catch (error) {
    return next(error);
  }
};

// Get current user profile
const getProfile = async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id).populate('department');
    
    return sendSuccess(res, 'Profile retrieved successfully', user);
  } catch (error) {
    return next(error);
  }
};

// Update user profile
const updateProfile = async (req, res, next) => {
  try {
    const { name, phone, designation, qualifications, specializations } = req.body;

    const user = await User.findByIdAndUpdate(
      req.user._id,
      {
        name,
        phone,
        designation,
        qualifications,
        specializations,
        updatedBy: req.user._id,
      },
      { new: true, runValidators: true }
    ).populate('department');

    return sendSuccess(res, 'Profile updated successfully', user);
  } catch (error) {
    return next(error);
  }
};

// Change password
const changePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Get user with password
    const user = await User.findById(req.user._id).select('+passwordHash');

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return next(new AppError('Current password is incorrect', 400));
    }

    // Update password
    user.passwordHash = newPassword; // Will be hashed by pre-save middleware
    await user.save();

    logger.info(`Password changed for user: ${user.email}`);

    return sendSuccess(res, 'Password changed successfully');
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  registerValidation,
  loginValidation,
  handleValidationErrors,
};
