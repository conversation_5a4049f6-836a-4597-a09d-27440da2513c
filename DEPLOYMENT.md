# Deployment Guide

This guide covers deploying the NBA Accreditation Readiness & OBE Attainment Suite to production environments.

## 🚀 Production Deployment Options

### Option 1: Docker Deployment (Recommended)

#### Prerequisites
- Docker and Docker Compose installed
- Domain name configured
- SSL certificate (Let's Encrypt recommended)

#### 1. Create Docker Compose Configuration

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:5.0
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME}
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

  backend:
    build:
      context: ./server
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/${MONGO_DB_NAME}?authSource=admin
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      CORS_ORIGIN: ${FRONTEND_URL}
    depends_on:
      - mongodb
    volumes:
      - ./server/uploads:/app/uploads
    networks:
      - app-network

  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.prod
      args:
        VITE_API_URL: ${BACKEND_URL}
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge
```

#### 2. Create Production Environment File

Create `.env.prod`:

```env
# Database
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-password
MONGO_DB_NAME=nba_obe_suite

# JWT Secrets
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-min-32-chars

# URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

#### 3. Create Production Dockerfiles

**Server Dockerfile (`server/Dockerfile.prod`):**

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

USER nodejs

EXPOSE 5000

CMD ["npm", "start"]
```

**Client Dockerfile (`client/Dockerfile.prod`):**

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

RUN npm run build

FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
```

#### 4. Deploy

```bash
# Build and start services
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop services
docker-compose -f docker-compose.prod.yml down
```

### Option 2: Cloud Platform Deployment

#### Vercel (Frontend) + Railway (Backend)

**Frontend on Vercel:**

1. Connect your GitHub repository to Vercel
2. Set build command: `cd client && npm run build`
3. Set output directory: `client/dist`
4. Add environment variables:
   ```
   VITE_API_URL=https://your-backend-url.railway.app/api
   ```

**Backend on Railway:**

1. Connect your GitHub repository to Railway
2. Set root directory: `server`
3. Add environment variables:
   ```
   NODE_ENV=production
   PORT=5000
   MONGODB_URI=your-mongodb-atlas-uri
   JWT_SECRET=your-jwt-secret
   JWT_REFRESH_SECRET=your-refresh-secret
   CORS_ORIGIN=https://your-frontend-url.vercel.app
   ```

#### Heroku Deployment

**Prepare for Heroku:**

Create `Procfile` in root:
```
web: cd server && npm start
```

Create `package.json` in root:
```json
{
  "name": "nba-obe-suite",
  "scripts": {
    "build": "cd client && npm install && npm run build",
    "start": "cd server && npm start",
    "heroku-postbuild": "npm run build"
  }
}
```

**Deploy to Heroku:**

```bash
# Install Heroku CLI and login
heroku login

# Create app
heroku create your-app-name

# Add MongoDB addon
heroku addons:create mongolab:sandbox

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-jwt-secret
heroku config:set JWT_REFRESH_SECRET=your-refresh-secret

# Deploy
git push heroku main
```

## 🔧 Production Configuration

### Environment Variables

**Required Environment Variables:**

```env
# Server
NODE_ENV=production
PORT=5000
MONGODB_URI=********************************:port/database
JWT_SECRET=minimum-32-character-secret-key
JWT_REFRESH_SECRET=minimum-32-character-refresh-secret
CORS_ORIGIN=https://yourdomain.com

# Client
VITE_API_URL=https://api.yourdomain.com/api
```

**Optional Environment Variables:**

```env
# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password

# Logging
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### Database Setup

**MongoDB Atlas (Recommended):**

1. Create MongoDB Atlas account
2. Create a new cluster
3. Create database user
4. Whitelist IP addresses (0.0.0.0/0 for cloud deployment)
5. Get connection string

**Local MongoDB:**

```bash
# Install MongoDB
sudo apt-get install mongodb

# Start MongoDB service
sudo systemctl start mongodb
sudo systemctl enable mongodb

# Create database and user
mongo
use nba_obe_suite
db.createUser({
  user: "admin",
  pwd: "password",
  roles: ["readWrite"]
})
```

### SSL Certificate Setup

**Using Let's Encrypt:**

```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx Configuration

Create `/etc/nginx/sites-available/nba-obe-suite`:

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    location / {
        root /var/www/nba-obe-suite;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔍 Monitoring & Maintenance

### Health Checks

Add health check endpoint in server:

```javascript
// server/src/routes/health.js
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});
```

### Logging

Configure production logging:

```javascript
// server/src/config/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

### Backup Strategy

**Database Backup:**

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="$MONGODB_URI" --out="/backups/backup_$DATE"
tar -czf "/backups/backup_$DATE.tar.gz" "/backups/backup_$DATE"
rm -rf "/backups/backup_$DATE"

# Keep only last 7 days
find /backups -name "backup_*.tar.gz" -mtime +7 -delete
```

**File Backup:**

```bash
#!/bin/bash
# file-backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "/backups/uploads_$DATE.tar.gz" "/app/uploads"
find /backups -name "uploads_*.tar.gz" -mtime +30 -delete
```

### Performance Optimization

**Server Optimizations:**

```javascript
// server/src/index.js
const compression = require('compression');
const helmet = require('helmet');

app.use(compression());
app.use(helmet());

// Enable gzip compression
app.use(express.static('public', {
  maxAge: '1y',
  setHeaders: (res, path) => {
    if (path.endsWith('.js') || path.endsWith('.css')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000');
    }
  }
}));
```

**Database Optimizations:**

```javascript
// Add indexes for frequently queried fields
db.users.createIndex({ email: 1 });
db.courses.createIndex({ programId: 1, semester: 1 });
db.assessments.createIndex({ courseId: 1, type: 1 });
db.results.createIndex({ assessmentId: 1, studentRoll: 1 });
```

## 🚨 Troubleshooting

### Common Issues

**1. CORS Errors:**
- Verify CORS_ORIGIN environment variable
- Check frontend URL configuration

**2. Database Connection:**
- Verify MongoDB URI format
- Check network connectivity
- Ensure database user permissions

**3. File Upload Issues:**
- Check upload directory permissions
- Verify MAX_FILE_SIZE setting
- Ensure sufficient disk space

**4. JWT Token Issues:**
- Verify JWT_SECRET length (minimum 32 characters)
- Check token expiration settings
- Ensure consistent secret across instances

### Logs Analysis

```bash
# View application logs
docker-compose logs -f backend

# View nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# View system logs
sudo journalctl -u your-app-service -f
```

### Performance Monitoring

Use tools like:
- **PM2** for Node.js process management
- **New Relic** or **DataDog** for APM
- **MongoDB Compass** for database monitoring
- **Nginx Amplify** for web server monitoring

---

For additional support, refer to the main README.md or create an issue in the repository.
