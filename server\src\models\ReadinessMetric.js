const mongoose = require('mongoose');

const ReadinessMetricSchema = new mongoose.Schema({
  programId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Program',
    required: [true, 'Program is required'],
  },
  code: {
    type: String,
    required: [true, 'Metric code is required'],
    uppercase: true,
    trim: true,
    maxlength: [50, 'Metric code cannot exceed 50 characters'],
  },
  name: {
    type: String,
    required: [true, 'Metric name is required'],
    trim: true,
    maxlength: [200, 'Metric name cannot exceed 200 characters'],
  },
  description: {
    type: String,
    required: [true, 'Metric description is required'],
    trim: true,
    maxlength: [1000, 'Metric description cannot exceed 1000 characters'],
  },
  category: {
    type: String,
    enum: {
      values: [
        'Faculty',
        'Curriculum',
        'StudentPerformance',
        'Infrastructure',
        'ContinuousImprovement',
        'Research',
        'Industry',
        'Governance'
      ],
      message: 'Invalid metric category',
    },
    required: [true, 'Metric category is required'],
  },
  subCategory: {
    type: String,
    trim: true,
    maxlength: [100, 'Sub-category cannot exceed 100 characters'],
  },
  // Metric values
  currentValue: {
    type: Number,
    required: [true, 'Current value is required'],
    min: [0, 'Current value cannot be negative'],
  },
  targetValue: {
    type: Number,
    required: [true, 'Target value is required'],
    min: [0, 'Target value cannot be negative'],
  },
  benchmarkValue: {
    type: Number,
    min: [0, 'Benchmark value cannot be negative'],
  },
  // Value configuration
  unit: {
    type: String,
    enum: {
      values: ['Number', 'Percentage', 'Ratio', 'Score', 'Count', 'Hours', 'Credits'],
      message: 'Invalid unit type',
    },
    required: [true, 'Unit is required'],
  },
  dataType: {
    type: String,
    enum: {
      values: ['Integer', 'Decimal', 'Boolean', 'Text'],
      message: 'Invalid data type',
    },
    default: 'Decimal',
  },
  // Calculation configuration
  calculationMethod: {
    type: String,
    enum: {
      values: ['Manual', 'Automatic', 'Formula', 'Aggregated'],
      message: 'Invalid calculation method',
    },
    default: 'Manual',
  },
  formula: {
    type: String,
    trim: true,
    maxlength: [500, 'Formula cannot exceed 500 characters'],
  },
  // Weight and importance
  weight: {
    type: Number,
    required: [true, 'Metric weight is required'],
    min: [0, 'Weight cannot be negative'],
    max: [1, 'Weight cannot exceed 1'],
  },
  priority: {
    type: String,
    enum: {
      values: ['Critical', 'High', 'Medium', 'Low'],
      message: 'Priority must be one of: Critical, High, Medium, Low',
    },
    default: 'Medium',
  },
  // NBA mapping
  nbaMapping: {
    criterion: {
      type: String,
      enum: [
        'VisionMission',
        'ProgramOutcomes',
        'Curriculum',
        'TeachingLearning',
        'Infra',
        'StudentPerformance',
        'Faculty',
        'Finance',
        'ContinuousImprovement'
      ],
    },
    subCriterion: String,
    weightage: {
      type: Number,
      min: [0, 'NBA weightage cannot be negative'],
      max: [100, 'NBA weightage cannot exceed 100'],
    },
  },
  // Historical data
  historicalData: [{
    academicYear: {
      type: String,
      required: true,
      match: [/^\d{4}-\d{4}$/, 'Academic year format should be YYYY-YYYY'],
    },
    value: {
      type: Number,
      required: true,
      min: [0, 'Historical value cannot be negative'],
    },
    recordedAt: {
      type: Date,
      default: Date.now,
    },
    recordedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    comments: String,
  }],
  // Thresholds for status determination
  thresholds: {
    excellent: {
      type: Number,
      min: [0, 'Excellent threshold cannot be negative'],
    },
    good: {
      type: Number,
      min: [0, 'Good threshold cannot be negative'],
    },
    satisfactory: {
      type: Number,
      min: [0, 'Satisfactory threshold cannot be negative'],
    },
    needsImprovement: {
      type: Number,
      min: [0, 'Needs improvement threshold cannot be negative'],
    },
  },
  // Data sources and evidence
  dataSources: [{
    type: String,
    trim: true,
    maxlength: [200, 'Data source cannot exceed 200 characters'],
  }],
  evidenceIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Evidence',
  }],
  // Frequency and updates
  updateFrequency: {
    type: String,
    enum: {
      values: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Semester', 'Annual'],
      message: 'Invalid update frequency',
    },
    default: 'Semester',
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
  nextUpdateDue: {
    type: Date,
  },
  // Status and compliance
  status: {
    type: String,
    enum: {
      values: ['Excellent', 'Good', 'Satisfactory', 'Needs Improvement', 'Critical'],
      message: 'Invalid status',
    },
  },
  complianceStatus: {
    type: String,
    enum: {
      values: ['Compliant', 'Partially Compliant', 'Non-Compliant', 'Not Applicable'],
      message: 'Invalid compliance status',
    },
    default: 'Not Applicable',
  },
  // Action items
  actionItems: [{
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: [500, 'Action item description cannot exceed 500 characters'],
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    dueDate: Date,
    status: {
      type: String,
      enum: ['Pending', 'In Progress', 'Completed', 'Cancelled'],
      default: 'Pending',
    },
    priority: {
      type: String,
      enum: ['High', 'Medium', 'Low'],
      default: 'Medium',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes
ReadinessMetricSchema.index({ programId: 1, code: 1 }, { unique: true });
ReadinessMetricSchema.index({ category: 1 });
ReadinessMetricSchema.index({ priority: 1 });
ReadinessMetricSchema.index({ status: 1 });
ReadinessMetricSchema.index({ 'nbaMapping.criterion': 1 });
ReadinessMetricSchema.index({ isActive: 1 });

// Virtual for program details
ReadinessMetricSchema.virtual('program', {
  ref: 'Program',
  localField: 'programId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for evidence details
ReadinessMetricSchema.virtual('evidence', {
  ref: 'Evidence',
  localField: 'evidenceIds',
  foreignField: '_id',
});

// Virtual for readiness percentage
ReadinessMetricSchema.virtual('readinessPercentage').get(function() {
  if (this.targetValue === 0) return 0;
  return Math.min((this.currentValue / this.targetValue) * 100, 100);
});

// Virtual for achievement status
ReadinessMetricSchema.virtual('achievementStatus').get(function() {
  const percentage = this.readinessPercentage;
  
  if (this.thresholds.excellent && percentage >= this.thresholds.excellent) return 'Excellent';
  if (this.thresholds.good && percentage >= this.thresholds.good) return 'Good';
  if (this.thresholds.satisfactory && percentage >= this.thresholds.satisfactory) return 'Satisfactory';
  if (this.thresholds.needsImprovement && percentage >= this.thresholds.needsImprovement) return 'Needs Improvement';
  return 'Critical';
});

// Pre-save middleware to update status and next update due
ReadinessMetricSchema.pre('save', function(next) {
  // Update status based on thresholds
  this.status = this.achievementStatus;
  
  // Calculate next update due date
  if (this.updateFrequency && this.lastUpdated) {
    const nextUpdate = new Date(this.lastUpdated);
    switch (this.updateFrequency) {
      case 'Daily':
        nextUpdate.setDate(nextUpdate.getDate() + 1);
        break;
      case 'Weekly':
        nextUpdate.setDate(nextUpdate.getDate() + 7);
        break;
      case 'Monthly':
        nextUpdate.setMonth(nextUpdate.getMonth() + 1);
        break;
      case 'Quarterly':
        nextUpdate.setMonth(nextUpdate.getMonth() + 3);
        break;
      case 'Semester':
        nextUpdate.setMonth(nextUpdate.getMonth() + 6);
        break;
      case 'Annual':
        nextUpdate.setFullYear(nextUpdate.getFullYear() + 1);
        break;
    }
    this.nextUpdateDue = nextUpdate;
  }
  
  next();
});

// Static method to get metrics by program
ReadinessMetricSchema.statics.getByProgram = function(programId, category = null) {
  const filter = { programId, isActive: true };
  if (category) filter.category = category;
  
  return this.find(filter)
    .populate('program', 'name code')
    .populate('evidence', 'title type verificationStatus')
    .sort({ category: 1, priority: -1, code: 1 });
};

// Static method to calculate overall readiness
ReadinessMetricSchema.statics.calculateOverallReadiness = async function(programId) {
  const metrics = await this.find({ programId, isActive: true });
  
  if (metrics.length === 0) return 0;
  
  const weightedSum = metrics.reduce((sum, metric) => {
    return sum + (metric.readinessPercentage * metric.weight);
  }, 0);
  
  const totalWeight = metrics.reduce((sum, metric) => sum + metric.weight, 0);
  
  return totalWeight > 0 ? (weightedSum / totalWeight) : 0;
};

// Instance method to update value
ReadinessMetricSchema.methods.updateValue = function(newValue, userId, comments = '') {
  // Add to historical data
  this.historicalData.push({
    academicYear: this.getCurrentAcademicYear(),
    value: this.currentValue,
    recordedAt: new Date(),
    recordedBy: userId,
    comments: comments,
  });
  
  // Update current value
  this.currentValue = newValue;
  this.lastUpdated = new Date();
  this.updatedBy = userId;
  
  return this.save();
};

// Instance method to get current academic year
ReadinessMetricSchema.methods.getCurrentAcademicYear = function() {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  // Assuming academic year starts in July
  if (currentMonth >= 7) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
};

// Instance method to add action item
ReadinessMetricSchema.methods.addActionItem = function(description, assignedTo, dueDate, priority = 'Medium') {
  this.actionItems.push({
    description,
    assignedTo,
    dueDate,
    priority,
  });
  
  return this.save();
};

module.exports = mongoose.model('ReadinessMetric', ReadinessMetricSchema);
