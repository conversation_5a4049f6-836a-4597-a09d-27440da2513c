const { body, query } = require('express-validator');
const Program = require('../models/Program');
const Department = require('../models/Department');
const Course = require('../models/Course');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createProgramValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Program name must be between 2 and 200 characters'),
  body('code')
    .trim()
    .isLength({ min: 2, max: 20 })
    .matches(/^[A-Z0-9\-]+$/)
    .withMessage('Program code must contain only uppercase letters, numbers, and hyphens'),
  body('deptId')
    .isMongoId()
    .withMessage('Invalid department ID'),
  body('level')
    .isIn(['UG', 'PG', 'PhD', 'Diploma', 'Certificate'])
    .withMessage('Invalid program level'),
  body('duration.years')
    .isInt({ min: 1, max: 10 })
    .withMessage('Duration years must be between 1 and 10'),
  body('duration.semesters')
    .isInt({ min: 2, max: 20 })
    .withMessage('Duration semesters must be between 2 and 20'),
  body('nbaCycle.startYear')
    .isInt({ min: 2000 })
    .withMessage('NBA cycle start year must be after 2000'),
  body('nbaCycle.endYear')
    .isInt()
    .custom((value, { req }) => {
      if (value <= req.body.nbaCycle.startYear) {
        throw new Error('NBA cycle end year must be after start year');
      }
      return true;
    }),
];

const updateProgramValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Program name must be between 2 and 200 characters'),
  body('level')
    .optional()
    .isIn(['UG', 'PG', 'PhD', 'Diploma', 'Certificate'])
    .withMessage('Invalid program level'),
  body('duration.years')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Duration years must be between 1 and 10'),
  body('duration.semesters')
    .optional()
    .isInt({ min: 2, max: 20 })
    .withMessage('Duration semesters must be between 2 and 20'),
];

// Get all programs with filtering
const getPrograms = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      deptId,
      level,
      isActive,
      search,
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (deptId) filter.deptId = deptId;
    if (level) filter.level = level;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      filter.deptId = req.user.deptId;
    }

    const skip = (page - 1) * limit;

    const [programs, total] = await Promise.all([
      Program.find(filter)
        .populate('department', 'name code')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Program.countDocuments(filter),
    ]);

    // Get statistics for each program
    const programsWithStats = await Promise.all(
      programs.map(async (program) => {
        const stats = await program.getStatistics();
        return {
          ...program.toObject(),
          statistics: stats,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Programs retrieved successfully', programsWithStats, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get program by ID
const getProgramById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const program = await Program.findById(id)
      .populate('department', 'name code description');

    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Non-admin users can only view programs from their department
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const statistics = await program.getStatistics();

    return sendSuccess(res, 'Program retrieved successfully', {
      ...program.toObject(),
      statistics,
    });
  } catch (error) {
    return next(error);
  }
};

// Create new program (Admin and Dept Head only)
const createProgram = async (req, res, next) => {
  try {
    const {
      name,
      code,
      deptId,
      description,
      duration,
      level,
      pos,
      psos,
      nbaCycle,
      regulations,
      admission,
    } = req.body;

    // Check if program code already exists
    const existingProgram = await Program.findByCode(code);
    if (existingProgram) {
      return next(new AppError('Program with this code already exists', 409));
    }

    // Validate department
    const department = await Department.findById(deptId);
    if (!department || !department.isActive) {
      return next(new AppError('Invalid or inactive department', 400));
    }

    // Department heads can only create programs in their department
    if (req.user.role === 'DEPT_HEAD' && deptId !== req.user.deptId.toString()) {
      return next(new AppError('Access denied to this department', 403));
    }

    const program = new Program({
      name,
      code: code.toUpperCase(),
      deptId,
      description,
      duration,
      level,
      pos: pos || [],
      psos: psos || [],
      nbaCycle,
      regulations,
      admission,
      createdBy: req.user._id,
    });

    await program.save();

    const populatedProgram = await Program.findById(program._id)
      .populate('department', 'name code');

    return sendCreated(res, 'Program created successfully', populatedProgram);
  } catch (error) {
    return next(error);
  }
};

// Update program (Admin and Dept Head only)
const updateProgram = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const program = await Program.findById(id);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Department heads can only update programs in their department
    if (req.user.role === 'DEPT_HEAD' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Add updatedBy field
    updateData.updatedBy = req.user._id;

    const updatedProgram = await Program.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('department', 'name code');

    return sendSuccess(res, 'Program updated successfully', updatedProgram);
  } catch (error) {
    return next(error);
  }
};

// Update Program Outcomes (POs)
const updatePOs = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { pos } = req.body;

    const program = await Program.findById(id);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Department heads can only update programs in their department
    if (req.user.role === 'DEPT_HEAD' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate PO codes for uniqueness
    const poCodes = pos.map(po => po.code);
    const duplicates = poCodes.filter((code, index) => poCodes.indexOf(code) !== index);
    if (duplicates.length > 0) {
      return next(new AppError(`Duplicate PO codes found: ${duplicates.join(', ')}`, 400));
    }

    program.pos = pos;
    program.updatedBy = req.user._id;
    await program.save();

    return sendSuccess(res, 'Program Outcomes updated successfully', program.pos);
  } catch (error) {
    return next(error);
  }
};

// Update Program Specific Outcomes (PSOs)
const updatePSOs = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { psos } = req.body;

    const program = await Program.findById(id);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Department heads can only update programs in their department
    if (req.user.role === 'DEPT_HEAD' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate PSO codes for uniqueness
    const psoCodes = psos.map(pso => pso.code);
    const duplicates = psoCodes.filter((code, index) => psoCodes.indexOf(code) !== index);
    if (duplicates.length > 0) {
      return next(new AppError(`Duplicate PSO codes found: ${duplicates.join(', ')}`, 400));
    }

    program.psos = psos;
    program.updatedBy = req.user._id;
    await program.save();

    return sendSuccess(res, 'Program Specific Outcomes updated successfully', program.psos);
  } catch (error) {
    return next(error);
  }
};

// Get program courses
const getProgramCourses = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { year, semester } = req.query;

    const program = await Program.findById(id);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Non-admin users can only view programs from their department
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const filter = { programId: id, isActive: true };
    if (year) filter.year = parseInt(year);
    if (semester) filter.semester = parseInt(semester);

    const courses = await Course.find(filter)
      .populate('faculty', 'name email')
      .populate('coordinatorDetails', 'name email')
      .sort({ semester: 1, code: 1 });

    return sendSuccess(res, 'Program courses retrieved successfully', courses);
  } catch (error) {
    return next(error);
  }
};

// Deactivate program (Admin only)
const deactivateProgram = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if program has active courses
    const activeCourses = await Course.countDocuments({ programId: id, isActive: true });
    if (activeCourses > 0) {
      return next(new AppError('Cannot deactivate program with active courses', 400));
    }

    const program = await Program.findByIdAndUpdate(
      id,
      { isActive: false, updatedBy: req.user._id },
      { new: true }
    );

    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    return sendSuccess(res, 'Program deactivated successfully', program);
  } catch (error) {
    return next(error);
  }
};

// Activate program (Admin only)
const activateProgram = async (req, res, next) => {
  try {
    const { id } = req.params;

    const program = await Program.findByIdAndUpdate(
      id,
      { isActive: true, updatedBy: req.user._id },
      { new: true }
    );

    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    return sendSuccess(res, 'Program activated successfully', program);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getPrograms,
  getProgramById,
  createProgram,
  updateProgram,
  updatePOs,
  updatePSOs,
  getProgramCourses,
  deactivateProgram,
  activateProgram,
  createProgramValidation,
  updateProgramValidation,
  handleValidationErrors,
};
