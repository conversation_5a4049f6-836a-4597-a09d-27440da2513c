const obeCalculationService = require('../services/obeCalculationService');
const Course = require('../models/Course');
const Program = require('../models/Program');
const { AppError } = require('../middleware/errorHandler');
const { sendSuccess, sendNotFound } = require('../utils/response');

// Get course attainment
const getCourseAttainment = async (req, res, next) => {
  try {
    const { courseId } = req.params;
    const { academicYear } = req.query;

    // Validate course
    const course = await Course.findById(courseId).populate('program');
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Use current academic year if not provided
    const targetAcademicYear = academicYear || getCurrentAcademicYear();

    const attainment = await obeCalculationService.calculateCourseAttainment(
      courseId, 
      targetAcademicYear
    );

    return sendSuccess(res, 'Course attainment calculated successfully', attainment);
  } catch (error) {
    return next(error);
  }
};

// Get program attainment
const getProgramAttainment = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { academicYear } = req.query;

    // Validate program
    const program = await Program.findById(programId);
    if (!program) {
      return sendNotFound(res, 'Program not found');
    }

    // Check access permissions
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Use current academic year if not provided
    const targetAcademicYear = academicYear || getCurrentAcademicYear();

    const attainment = await obeCalculationService.calculateProgramAttainment(
      programId, 
      targetAcademicYear
    );

    return sendSuccess(res, 'Program attainment calculated successfully', attainment);
  } catch (error) {
    return next(error);
  }
};

// Get attainment summary for multiple courses
const getAttainmentSummary = async (req, res, next) => {
  try {
    const { programId, semester, year } = req.query;

    // Build filter
    const filter = { isActive: true };
    if (programId) filter.programId = programId;
    if (semester) filter.semester = parseInt(semester);
    if (year) filter.year = parseInt(year);

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      if (req.user.role === 'FACULTY') {
        // Faculty can see courses they teach or coordinate
        filter.$or = [
          { facultyIds: req.user._id },
          { coordinator: req.user._id }
        ];
      } else {
        // Dept Head can see all courses from their department
        const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
        const programIds = programs.map(p => p._id);
        filter.programId = { $in: programIds };
      }
    }

    const courses = await Course.find(filter)
      .populate('program', 'name code')
      .select('_id title code semester year programId');

    const academicYear = year ? `${year}-${year + 1}` : getCurrentAcademicYear();
    const summaryData = [];

    // Calculate attainment for each course
    for (const course of courses) {
      try {
        const attainment = await obeCalculationService.calculateCourseAttainment(
          course._id, 
          academicYear
        );

        // Calculate summary statistics
        const coAttainmentValues = Object.values(attainment.coAttainment);
        const poAttainmentValues = Object.values(attainment.poAttainment);

        const coStats = calculateAttainmentStats(coAttainmentValues.map(co => co.finalPercentage));
        const poStats = calculateAttainmentStats(poAttainmentValues.map(po => po.finalPercentage));

        summaryData.push({
          courseId: course._id,
          courseCode: course.code,
          courseTitle: course.title,
          semester: course.semester,
          year: course.year,
          program: course.program,
          coAttainmentSummary: {
            totalCOs: coAttainmentValues.length,
            attainedCOs: coAttainmentValues.filter(co => co.attained).length,
            averageAttainment: coStats.average,
            minAttainment: coStats.min,
            maxAttainment: coStats.max
          },
          poAttainmentSummary: {
            totalPOs: poAttainmentValues.length,
            attainedPOs: poAttainmentValues.filter(po => po.attained).length,
            averageAttainment: poStats.average,
            minAttainment: poStats.min,
            maxAttainment: poStats.max
          },
          overallStatus: getOverallStatus(coAttainmentValues, poAttainmentValues)
        });
      } catch (error) {
        // Log error but continue with other courses
        console.error(`Error calculating attainment for course ${course.code}:`, error.message);
        summaryData.push({
          courseId: course._id,
          courseCode: course.code,
          courseTitle: course.title,
          semester: course.semester,
          year: course.year,
          program: course.program,
          error: error.message
        });
      }
    }

    return sendSuccess(res, 'Attainment summary retrieved successfully', {
      academicYear,
      totalCourses: courses.length,
      successfulCalculations: summaryData.filter(item => !item.error).length,
      failedCalculations: summaryData.filter(item => item.error).length,
      courses: summaryData
    });
  } catch (error) {
    return next(error);
  }
};

// Get attainment trends over multiple academic years
const getAttainmentTrends = async (req, res, next) => {
  try {
    const { courseId, programId, startYear, endYear } = req.query;

    if (!courseId && !programId) {
      return next(new AppError('Either courseId or programId is required', 400));
    }

    const start = parseInt(startYear) || new Date().getFullYear() - 3;
    const end = parseInt(endYear) || new Date().getFullYear();

    const trends = [];

    for (let year = start; year <= end; year++) {
      const academicYear = `${year}-${year + 1}`;
      
      try {
        let attainment;
        if (courseId) {
          // Validate course access
          const course = await Course.findById(courseId).populate('program');
          if (!course) continue;

          if (req.user.role === 'FACULTY') {
            const isFaculty = course.facultyIds.includes(req.user._id) || 
                             course.coordinator.toString() === req.user._id.toString();
            if (!isFaculty) continue;
          }

          if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
            continue;
          }

          attainment = await obeCalculationService.calculateCourseAttainment(courseId, academicYear);
        } else {
          // Validate program access
          const program = await Program.findById(programId);
          if (!program) continue;

          if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
            continue;
          }

          attainment = await obeCalculationService.calculateProgramAttainment(programId, academicYear);
        }

        // Extract trend data
        const coAttainmentValues = Object.values(attainment.coAttainment || {});
        const poAttainmentValues = Object.values(attainment.poAttainment || {});

        trends.push({
          academicYear,
          year,
          coAttainment: {
            average: calculateAttainmentStats(coAttainmentValues.map(co => co.finalPercentage)).average,
            attainedCount: coAttainmentValues.filter(co => co.attained).length,
            totalCount: coAttainmentValues.length
          },
          poAttainment: {
            average: calculateAttainmentStats(poAttainmentValues.map(po => po.finalPercentage)).average,
            attainedCount: poAttainmentValues.filter(po => po.attained).length,
            totalCount: poAttainmentValues.length
          }
        });
      } catch (error) {
        // Log error but continue with other years
        console.error(`Error calculating trends for ${academicYear}:`, error.message);
      }
    }

    return sendSuccess(res, 'Attainment trends retrieved successfully', {
      entity: courseId ? 'course' : 'program',
      entityId: courseId || programId,
      startYear: start,
      endYear: end,
      trends
    });
  } catch (error) {
    return next(error);
  }
};

// Get CO-PO mapping analysis
const getCOPOAnalysis = async (req, res, next) => {
  try {
    const { courseId } = req.params;

    // Validate course
    const course = await Course.findById(courseId).populate('program');
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate mapping
    const mappingValidation = await course.validateMapping();

    // Create mapping matrix
    const mappingMatrix = createMappingMatrix(course);

    // Calculate mapping statistics
    const mappingStats = calculateMappingStats(course);

    return sendSuccess(res, 'CO-PO mapping analysis retrieved successfully', {
      courseId,
      courseCode: course.code,
      courseTitle: course.title,
      validation: mappingValidation,
      mappingMatrix,
      statistics: mappingStats,
      recommendations: generateMappingRecommendations(mappingStats)
    });
  } catch (error) {
    return next(error);
  }
};

// Helper functions
function getCurrentAcademicYear() {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  // Assuming academic year starts in July
  if (currentMonth >= 7) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
}

function calculateAttainmentStats(values) {
  if (values.length === 0) {
    return { average: 0, min: 0, max: 0 };
  }

  const sum = values.reduce((acc, val) => acc + val, 0);
  return {
    average: Math.round((sum / values.length) * 100) / 100,
    min: Math.min(...values),
    max: Math.max(...values)
  };
}

function getOverallStatus(coAttainmentValues, poAttainmentValues) {
  const coAttainedPercentage = coAttainmentValues.length > 0 ? 
    (coAttainmentValues.filter(co => co.attained).length / coAttainmentValues.length) * 100 : 0;
  
  const poAttainedPercentage = poAttainmentValues.length > 0 ? 
    (poAttainmentValues.filter(po => po.attained).length / poAttainmentValues.length) * 100 : 0;

  const overallPercentage = (coAttainedPercentage + poAttainedPercentage) / 2;

  if (overallPercentage >= 90) return 'Excellent';
  if (overallPercentage >= 80) return 'Very Good';
  if (overallPercentage >= 70) return 'Good';
  if (overallPercentage >= 60) return 'Satisfactory';
  return 'Needs Improvement';
}

function createMappingMatrix(course) {
  const matrix = {};
  
  // Initialize matrix
  course.cos.forEach(co => {
    matrix[`CO${co.index}`] = {};
    course.program.pos.forEach(po => {
      matrix[`CO${co.index}`][po.code] = { level: 0, weight: 0 };
    });
  });

  // Fill matrix with mapping data
  course.mapping.po.forEach(mapping => {
    const coKey = `CO${mapping.coIndex}`;
    if (matrix[coKey] && matrix[coKey][mapping.poCode]) {
      matrix[coKey][mapping.poCode] = {
        level: mapping.level,
        weight: mapping.weight
      };
    }
  });

  return matrix;
}

function calculateMappingStats(course) {
  const stats = {
    totalCOs: course.cos.length,
    totalPOs: course.program.pos.length,
    totalMappings: course.mapping.po.length,
    mappingDensity: 0,
    levelDistribution: { L1: 0, L2: 0, L3: 0 },
    unmappedCOs: [],
    unmappedPOs: []
  };

  const maxPossibleMappings = stats.totalCOs * stats.totalPOs;
  stats.mappingDensity = maxPossibleMappings > 0 ? 
    (stats.totalMappings / maxPossibleMappings) * 100 : 0;

  // Calculate level distribution
  course.mapping.po.forEach(mapping => {
    stats.levelDistribution[`L${mapping.level}`]++;
  });

  // Find unmapped COs and POs
  const mappedCOs = new Set(course.mapping.po.map(m => m.coIndex));
  const mappedPOs = new Set(course.mapping.po.map(m => m.poCode));

  course.cos.forEach(co => {
    if (!mappedCOs.has(co.index)) {
      stats.unmappedCOs.push(co.index);
    }
  });

  course.program.pos.forEach(po => {
    if (!mappedPOs.has(po.code)) {
      stats.unmappedPOs.push(po.code);
    }
  });

  return stats;
}

function generateMappingRecommendations(stats) {
  const recommendations = [];

  if (stats.mappingDensity < 30) {
    recommendations.push({
      type: 'warning',
      message: 'Low mapping density. Consider adding more CO-PO mappings to ensure comprehensive coverage.'
    });
  }

  if (stats.unmappedCOs.length > 0) {
    recommendations.push({
      type: 'error',
      message: `Unmapped COs found: ${stats.unmappedCOs.join(', ')}. All COs should be mapped to at least one PO.`
    });
  }

  if (stats.unmappedPOs.length > 0) {
    recommendations.push({
      type: 'info',
      message: `Unmapped POs found: ${stats.unmappedPOs.join(', ')}. Consider if these POs should be addressed by this course.`
    });
  }

  if (stats.levelDistribution.L3 === 0) {
    recommendations.push({
      type: 'suggestion',
      message: 'No Level 3 mappings found. Consider adding higher-order thinking skill mappings for better learning outcomes.'
    });
  }

  return recommendations;
}

module.exports = {
  getCourseAttainment,
  getProgramAttainment,
  getAttainmentSummary,
  getAttainmentTrends,
  getCOPOAnalysis,
};
