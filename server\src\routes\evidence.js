const express = require('express');
const {
  getEvidence,
  getEvidenceById,
  createEvidence,
  updateEvidence,
  verifyEvidence,
  deleteEvidence,
  downloadEvidence,
  getEvidenceByCriterion,
  upload,
  createEvidenceValidation,
  handleValidationErrors,
} = require('../controllers/evidenceController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all evidence (with filtering and search)
router.get('/', getEvidence);

// Get evidence by NBA criterion
router.get('/criterion/:criterion', getEvidenceByCriterion);

// Get evidence by ID
router.get('/:id', getEvidenceById);

// Download evidence file
router.get('/:id/download', downloadEvidence);

// Create new evidence (with file upload)
router.post('/', upload.single('file'), createEvidenceValidation, handleValidationErrors, createEvidence);

// Update evidence (Admin, Dept Head, and creator)
router.put('/:id', updateEvidence);

// Verify evidence (Admin and Dept Head only)
router.patch('/:id/verify', authorize('ADMIN', 'DEPT_HEAD'), verifyEvidence);

// Delete evidence (Admin, Dept Head, and creator)
router.delete('/:id', deleteEvidence);

module.exports = router;
