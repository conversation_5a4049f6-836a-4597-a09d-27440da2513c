const express = require('express');
const {
  getCourseAttainment,
  getProgramAttainment,
  getAttainmentSummary,
  getAttainmentTrends,
  getCOPOAnalysis,
} = require('../controllers/attainmentController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get attainment summary for multiple courses
router.get('/summary', getAttainmentSummary);

// Get attainment trends over multiple years
router.get('/trends', getAttainmentTrends);

// Get course attainment
router.get('/course/:courseId', getCourseAttainment);

// Get program attainment
router.get('/program/:programId', getProgramAttainment);

// Get CO-PO mapping analysis
router.get('/course/:courseId/co-po-analysis', getCOPOAnalysis);

module.exports = router;
