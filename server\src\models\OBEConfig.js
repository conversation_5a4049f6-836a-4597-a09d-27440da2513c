const mongoose = require('mongoose');

const OBEConfigSchema = new mongoose.Schema({
  programId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Program',
    required: [true, 'Program is required'],
    unique: true,
  },
  // Direct vs Indirect assessment weights
  directWeight: {
    type: Number,
    required: [true, 'Direct assessment weight is required'],
    min: [0, 'Direct weight cannot be negative'],
    max: [1, 'Direct weight cannot exceed 1'],
    default: 0.8,
  },
  indirectWeight: {
    type: Number,
    required: [true, 'Indirect assessment weight is required'],
    min: [0, 'Indirect weight cannot be negative'],
    max: [1, 'Indirect weight cannot exceed 1'],
    default: 0.2,
  },
  // CO-PO mapping level weights (L1: 1, L2: 2, L3: 3)
  levelWeights: {
    L1: {
      type: Number,
      default: 1,
      min: [0, 'L1 weight cannot be negative'],
    },
    L2: {
      type: Number,
      default: 2,
      min: [0, 'L2 weight cannot be negative'],
    },
    L3: {
      type: Number,
      default: 3,
      min: [0, 'L3 weight cannot be negative'],
    },
  },
  // Attainment threshold bands
  attainmentBands: [{
    min: {
      type: Number,
      required: true,
      min: [0, 'Minimum percentage cannot be negative'],
      max: [100, 'Minimum percentage cannot exceed 100'],
    },
    max: {
      type: Number,
      required: true,
      min: [0, 'Maximum percentage cannot be negative'],
      max: [100, 'Maximum percentage cannot exceed 100'],
    },
    label: {
      type: String,
      required: true,
      trim: true,
      maxlength: [20, 'Label cannot exceed 20 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, 'Description cannot exceed 200 characters'],
    },
  }],
  // CO attainment calculation method
  coAttainmentMethod: {
    type: String,
    enum: {
      values: ['Average', 'Weighted Average', 'Best Performance', 'Threshold Based'],
      message: 'CO attainment method must be one of: Average, Weighted Average, Best Performance, Threshold Based',
    },
    default: 'Weighted Average',
  },
  // PO attainment aggregation strategy
  poAggregationStrategy: {
    type: String,
    enum: {
      values: ['Mean', 'Credit Weighted', 'Top N COs', 'Minimum Threshold'],
      message: 'PO aggregation strategy must be one of: Mean, Credit Weighted, Top N COs, Minimum Threshold',
    },
    default: 'Credit Weighted',
  },
  // Target thresholds
  targets: {
    coTarget: {
      type: Number,
      default: 70,
      min: [0, 'CO target cannot be negative'],
      max: [100, 'CO target cannot exceed 100'],
    },
    poTarget: {
      type: Number,
      default: 70,
      min: [0, 'PO target cannot be negative'],
      max: [100, 'PO target cannot exceed 100'],
    },
    psoTarget: {
      type: Number,
      default: 70,
      min: [0, 'PSO target cannot be negative'],
      max: [100, 'PSO target cannot exceed 100'],
    },
  },
  // Assessment weightages for different types
  assessmentWeights: {
    T1: {
      type: Number,
      default: 0.15,
      min: [0, 'T1 weight cannot be negative'],
      max: [1, 'T1 weight cannot exceed 1'],
    },
    T2: {
      type: Number,
      default: 0.15,
      min: [0, 'T2 weight cannot be negative'],
      max: [1, 'T2 weight cannot exceed 1'],
    },
    EndSem: {
      type: Number,
      default: 0.50,
      min: [0, 'EndSem weight cannot be negative'],
      max: [1, 'EndSem weight cannot exceed 1'],
    },
    Assignment: {
      type: Number,
      default: 0.10,
      min: [0, 'Assignment weight cannot be negative'],
      max: [1, 'Assignment weight cannot exceed 1'],
    },
    Lab: {
      type: Number,
      default: 0.10,
      min: [0, 'Lab weight cannot be negative'],
      max: [1, 'Lab weight cannot exceed 1'],
    },
  },
  // Indirect assessment configuration
  indirectAssessment: {
    studentSurveyWeight: {
      type: Number,
      default: 0.6,
      min: [0, 'Student survey weight cannot be negative'],
      max: [1, 'Student survey weight cannot exceed 1'],
    },
    alumniSurveyWeight: {
      type: Number,
      default: 0.3,
      min: [0, 'Alumni survey weight cannot be negative'],
      max: [1, 'Alumni survey weight cannot exceed 1'],
    },
    employerSurveyWeight: {
      type: Number,
      default: 0.1,
      min: [0, 'Employer survey weight cannot be negative'],
      max: [1, 'Employer survey weight cannot exceed 1'],
    },
    minimumSampleSize: {
      type: Number,
      default: 30,
      min: [1, 'Minimum sample size must be at least 1'],
    },
  },
  // Academic year configuration
  academicYear: {
    startMonth: {
      type: Number,
      default: 7, // July
      min: [1, 'Start month must be between 1 and 12'],
      max: [12, 'Start month must be between 1 and 12'],
    },
    endMonth: {
      type: Number,
      default: 6, // June
      min: [1, 'End month must be between 1 and 12'],
      max: [12, 'End month must be between 1 and 12'],
    },
  },
  // Calculation preferences
  calculationPreferences: {
    roundingPrecision: {
      type: Number,
      default: 2,
      min: [0, 'Rounding precision cannot be negative'],
      max: [5, 'Rounding precision cannot exceed 5'],
    },
    excludeAbsentStudents: {
      type: Boolean,
      default: true,
    },
    excludeMalpracticeStudents: {
      type: Boolean,
      default: true,
    },
    useNormalizedScores: {
      type: Boolean,
      default: false,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes (programId index is automatically created by unique: true)
OBEConfigSchema.index({ isActive: 1 });

// Virtual for program details
OBEConfigSchema.virtual('program', {
  ref: 'Program',
  localField: 'programId',
  foreignField: '_id',
  justOne: true,
});

// Pre-save middleware to validate weights
OBEConfigSchema.pre('save', function(next) {
  // Validate direct + indirect weights sum to 1
  if (Math.abs((this.directWeight + this.indirectWeight) - 1) > 0.01) {
    return next(new Error('Direct and indirect weights must sum to 1.0'));
  }

  // Validate assessment weights sum to 1 or less
  const assessmentWeightSum = Object.values(this.assessmentWeights).reduce((sum, weight) => sum + weight, 0);
  if (assessmentWeightSum > 1.01) { // Allow small floating point errors
    return next(new Error('Assessment weights cannot sum to more than 1.0'));
  }

  // Validate indirect assessment weights sum to 1
  const indirectWeightSum = this.indirectAssessment.studentSurveyWeight + 
                           this.indirectAssessment.alumniSurveyWeight + 
                           this.indirectAssessment.employerSurveyWeight;
  if (Math.abs(indirectWeightSum - 1) > 0.01) {
    return next(new Error('Indirect assessment weights must sum to 1.0'));
  }

  // Validate attainment bands don't overlap
  const sortedBands = this.attainmentBands.sort((a, b) => a.min - b.min);
  for (let i = 0; i < sortedBands.length - 1; i++) {
    if (sortedBands[i].max >= sortedBands[i + 1].min) {
      return next(new Error('Attainment bands cannot overlap'));
    }
  }

  next();
});

// Static method to get config by program
OBEConfigSchema.statics.getByProgram = function(programId) {
  return this.findOne({ programId, isActive: true }).populate('program');
};

// Static method to get default config
OBEConfigSchema.statics.getDefaultConfig = function() {
  return {
    directWeight: 0.8,
    indirectWeight: 0.2,
    levelWeights: { L1: 1, L2: 2, L3: 3 },
    attainmentBands: [
      { min: 90, max: 100, label: 'A++', description: 'Excellent' },
      { min: 80, max: 89, label: 'A+', description: 'Very Good' },
      { min: 70, max: 79, label: 'A', description: 'Good' },
      { min: 60, max: 69, label: 'B', description: 'Satisfactory' },
      { min: 50, max: 59, label: 'C', description: 'Needs Improvement' },
      { min: 0, max: 49, label: 'F', description: 'Not Attained' },
    ],
    targets: { coTarget: 70, poTarget: 70, psoTarget: 70 },
    assessmentWeights: {
      T1: 0.15,
      T2: 0.15,
      EndSem: 0.50,
      Assignment: 0.10,
      Lab: 0.10,
    },
  };
};

// Instance method to get attainment band for percentage
OBEConfigSchema.methods.getAttainmentBand = function(percentage) {
  return this.attainmentBands.find(band => 
    percentage >= band.min && percentage <= band.max
  );
};

// Instance method to calculate weighted level impact
OBEConfigSchema.methods.calculateLevelImpact = function(level) {
  return this.levelWeights[`L${level}`] || 1;
};

module.exports = mongoose.model('OBEConfig', OBEConfigSchema);
