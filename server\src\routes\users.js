const express = require('express');
const {
  getUsers,
  getUserById,
  updateUser,
  deactivateUser,
  activateUser,
  getUsersByRole,
  getDepartmentStats,
  updateUserValidation,
  handleValidationErrors,
} = require('../controllers/userController');
const { authenticate, authorize, authorizeDepartment } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all users (with filtering and pagination)
router.get('/', authorize('ADMIN', 'DEPT_HEAD'), getUsers);

// Get users by role
router.get('/role/:role', authorize('ADMIN', 'DEPT_HEAD'), getUsersByRole);

// Get department statistics
router.get('/department/:deptId/stats', authorize('ADMIN', 'DEPT_HEAD'), authorizeDepartment, getDepartmentStats);

// Get user by ID
router.get('/:id', authorize('ADMIN', 'DEPT_HEAD'), getUserById);

// Update user
router.put('/:id', authorize('ADMIN', 'DEPT_HEAD'), updateUserValidation, handleValidationErrors, updateUser);

// Deactivate user (Admin only)
router.put('/:id/deactivate', authorize('ADMIN'), deactivateUser);

// Activate user (Admin only)
router.put('/:id/activate', authorize('ADMIN'), activateUser);

module.exports = router;
