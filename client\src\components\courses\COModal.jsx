import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const COModal = ({ isOpen, onClose, onSubmit, course }) => {
  const [loading, setLoading] = useState(false);
  const [cos, setCos] = useState([]);

  useEffect(() => {
    if (course && course.cos) {
      setCos(course.cos.map(co => ({ ...co })));
    } else {
      setCos([]);
    }
  }, [course]);

  const addCO = () => {
    const newIndex = cos.length + 1;
    setCos([...cos, {
      index: newIndex,
      statement: '',
      target: 70,
      bloomsLevel: 'Remember',
    }]);
  };

  const removeCO = (index) => {
    const updatedCos = cos.filter((_, i) => i !== index);
    // Reindex the remaining COs
    const reindexedCos = updatedCos.map((co, i) => ({
      ...co,
      index: i + 1,
    }));
    setCos(reindexedCos);
  };

  const updateCO = (index, field, value) => {
    const updatedCos = cos.map((co, i) => {
      if (i === index) {
        return {
          ...co,
          [field]: field === 'target' ? parseFloat(value) || 0 : value,
        };
      }
      return co;
    });
    setCos(updatedCos);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (cos.length === 0) {
      toast.error('Please add at least one Course Outcome');
      return;
    }

    for (let i = 0; i < cos.length; i++) {
      const co = cos[i];
      if (!co.statement.trim()) {
        toast.error(`Please provide a statement for CO${i + 1}`);
        return;
      }
      if (co.target < 0 || co.target > 100) {
        toast.error(`Target for CO${i + 1} must be between 0 and 100`);
        return;
      }
    }

    setLoading(true);
    try {
      await onSubmit(cos);
    } catch (error) {
      console.error('Error submitting COs:', error);
    } finally {
      setLoading(false);
    }
  };

  const bloomsLevels = [
    'Remember',
    'Understand', 
    'Apply',
    'Analyze',
    'Evaluate',
    'Create'
  ];

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Manage Course Outcomes
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      {course?.code} - {course?.title}
                    </p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Course Outcomes List */}
                  <div className="space-y-4">
                    {cos.length === 0 ? (
                      <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                        <p className="text-gray-500">No Course Outcomes defined yet.</p>
                        <button
                          type="button"
                          onClick={addCO}
                          className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                          Add First CO
                        </button>
                      </div>
                    ) : (
                      cos.map((co, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900">
                              Course Outcome {co.index} (CO{co.index})
                            </h4>
                            <button
                              type="button"
                              onClick={() => removeCO(index)}
                              className="text-red-600 hover:text-red-800 focus:outline-none"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>

                          <div className="grid grid-cols-1 gap-4 sm:grid-cols-12">
                            {/* CO Statement */}
                            <div className="sm:col-span-8">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Statement *
                              </label>
                              <textarea
                                value={co.statement}
                                onChange={(e) => updateCO(index, 'statement', e.target.value)}
                                rows={2}
                                placeholder="Describe what students will be able to do after completing this course..."
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                required
                              />
                            </div>

                            {/* Bloom's Level */}
                            <div className="sm:col-span-2">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Bloom's Level *
                              </label>
                              <select
                                value={co.bloomsLevel}
                                onChange={(e) => updateCO(index, 'bloomsLevel', e.target.value)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                required
                              >
                                {bloomsLevels.map((level) => (
                                  <option key={level} value={level}>
                                    {level}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* Target */}
                            <div className="sm:col-span-2">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Target (%) *
                              </label>
                              <input
                                type="number"
                                value={co.target}
                                onChange={(e) => updateCO(index, 'target', e.target.value)}
                                min="0"
                                max="100"
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                required
                              />
                            </div>
                          </div>

                          {/* Bloom's Level Description */}
                          <div className="text-xs text-gray-500">
                            <strong>{co.bloomsLevel}:</strong>{' '}
                            {getBloomsDescription(co.bloomsLevel)}
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  {/* Add CO Button */}
                  {cos.length > 0 && (
                    <div className="flex justify-center">
                      <button
                        type="button"
                        onClick={addCO}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                        Add Another CO
                      </button>
                    </div>
                  )}

                  {/* Guidelines */}
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">Guidelines for Writing Course Outcomes</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Start with "Students will be able to..."</li>
                      <li>• Use action verbs that align with Bloom's taxonomy levels</li>
                      <li>• Be specific and measurable</li>
                      <li>• Focus on what students will achieve, not what they will learn</li>
                      <li>• Typically 4-6 COs per course</li>
                    </ul>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading || cos.length === 0}
                      className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : 'Save Course Outcomes'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

function getBloomsDescription(level) {
  const descriptions = {
    'Remember': 'Recall facts and basic concepts',
    'Understand': 'Explain ideas or concepts',
    'Apply': 'Use information in new situations',
    'Analyze': 'Draw connections among ideas',
    'Evaluate': 'Justify a stand or decision',
    'Create': 'Produce new or original work'
  };
  return descriptions[level] || '';
}
