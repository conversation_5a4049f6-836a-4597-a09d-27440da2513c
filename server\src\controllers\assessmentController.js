const { body } = require('express-validator');
const Assessment = require('../models/Assessment');
const Course = require('../models/Course');
const Program = require('../models/Program');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createAssessmentValidation = [
  body('courseId')
    .isMongoId()
    .withMessage('Invalid course ID'),
  body('title')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Assessment title must be between 2 and 200 characters'),
  body('type')
    .isIn(['T1', 'T2', 'EndSem', 'Quiz', 'Assignment', 'Lab', 'Project', 'Presentation', 'Viva'])
    .withMessage('Invalid assessment type'),
  body('maxMarks')
    .isInt({ min: 1, max: 1000 })
    .withMessage('Maximum marks must be between 1 and 1000'),
  body('weightage')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Weightage must be between 0 and 100'),
  body('date')
    .isISO8601()
    .withMessage('Invalid date format'),
  body('duration')
    .isInt({ min: 1, max: 600 })
    .withMessage('Duration must be between 1 and 600 minutes'),
];

const updateAssessmentValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Assessment title must be between 2 and 200 characters'),
  body('maxMarks')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Maximum marks must be between 1 and 1000'),
  body('weightage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Weightage must be between 0 and 100'),
  body('duration')
    .optional()
    .isInt({ min: 1, max: 600 })
    .withMessage('Duration must be between 1 and 600 minutes'),
];

// Get all assessments with filtering
const getAssessments = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      courseId,
      type,
      status,
      year,
      facultyId,
      search,
    } = req.query;

    // Build filter object
    const filter = { isActive: true };
    
    if (courseId) filter.courseId = courseId;
    if (type) filter.type = type;
    if (status) filter.status = status;
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    // For faculty, filter by courses they teach or coordinate
    if (req.user.role === 'FACULTY' || facultyId) {
      const targetFacultyId = facultyId || req.user._id;
      const courses = await Course.find({
        $or: [
          { facultyIds: targetFacultyId },
          { coordinator: targetFacultyId }
        ],
        ...(year && { year: parseInt(year) }),
        isActive: true
      }).select('_id');
      
      const courseIds = courses.map(c => c._id);
      filter.courseId = { $in: courseIds };
    }

    // For non-admin users, filter by their department's courses
    if (req.user.role !== 'ADMIN' && req.user.role !== 'FACULTY') {
      const programs = await Program.find({ deptId: req.user.deptId }).select('_id');
      const programIds = programs.map(p => p._id);
      const courses = await Course.find({ 
        programId: { $in: programIds },
        ...(year && { year: parseInt(year) }),
        isActive: true 
      }).select('_id');
      const courseIds = courses.map(c => c._id);
      filter.courseId = { $in: courseIds };
    }

    const skip = (page - 1) * limit;

    const [assessments, total] = await Promise.all([
      Assessment.find(filter)
        .populate({
          path: 'course',
          select: 'title code semester year programId',
          populate: {
            path: 'program',
            select: 'name code'
          }
        })
        .populate('createdBy', 'name email')
        .sort({ date: -1, createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Assessment.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Assessments retrieved successfully', assessments, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get assessment by ID
const getAssessmentById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const assessment = await Assessment.findById(id)
      .populate({
        path: 'course',
        select: 'title code semester year programId cos facultyIds coordinator',
        populate: [
          {
            path: 'program',
            select: 'name code pos psos'
          },
          {
            path: 'faculty',
            select: 'name email'
          },
          {
            path: 'coordinatorDetails',
            select: 'name email'
          }
        ]
      })
      .populate('createdBy', 'name email')
      .populate('evaluatedBy', 'name email');

    if (!assessment) {
      return sendNotFound(res, 'Assessment not found');
    }

    // Check access permissions
    const course = assessment.course;
    const program = await Program.findById(course.programId);
    
    // Faculty can only view assessments for courses they teach or coordinate
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }
    
    // Non-admin users can only view assessments from their department
    if (req.user.role !== 'ADMIN' && program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Calculate CO-wise statistics if assessment has results
    const coStatistics = await assessment.calculateCOStatistics();

    return sendSuccess(res, 'Assessment retrieved successfully', {
      ...assessment.toObject(),
      coStatistics,
    });
  } catch (error) {
    return next(error);
  }
};

// Create new assessment
const createAssessment = async (req, res, next) => {
  try {
    const {
      courseId,
      title,
      description,
      type,
      maxMarks,
      weightage,
      coTags,
      questions,
      date,
      duration,
      instructions,
      configuration,
      gradingScheme,
    } = req.body;

    // Validate course
    const course = await Course.findById(courseId).populate('program');
    if (!course || !course.isActive) {
      return next(new AppError('Invalid or inactive course', 400));
    }

    // Check access permissions
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate CO tags against course COs
    if (coTags && coTags.length > 0) {
      const courseCOIndices = course.cos.map(co => co.index);
      const invalidCOs = coTags.filter(tag => !courseCOIndices.includes(tag.coIndex));
      if (invalidCOs.length > 0) {
        return next(new AppError(`Invalid CO indices: ${invalidCOs.map(co => co.coIndex).join(', ')}`, 400));
      }
    }

    const assessment = new Assessment({
      courseId,
      title,
      description,
      type,
      maxMarks,
      weightage,
      coTags: coTags || [],
      questions: questions || [],
      date,
      duration,
      instructions,
      configuration,
      gradingScheme,
      createdBy: req.user._id,
    });

    await assessment.save();

    const populatedAssessment = await Assessment.findById(assessment._id)
      .populate('course', 'title code semester year')
      .populate('createdBy', 'name email');

    return sendCreated(res, 'Assessment created successfully', populatedAssessment);
  } catch (error) {
    return next(error);
  }
};

// Update assessment
const updateAssessment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const assessment = await Assessment.findById(id).populate({
      path: 'course',
      populate: { path: 'program' }
    });
    
    if (!assessment) {
      return sendNotFound(res, 'Assessment not found');
    }

    // Check access permissions
    const course = assessment.course;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Don't allow updates if assessment is already evaluated
    if (assessment.status === 'Completed') {
      return next(new AppError('Cannot update completed assessment', 400));
    }

    // Add updatedBy field
    updateData.updatedBy = req.user._id;

    const updatedAssessment = await Assessment.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('course', 'title code semester year')
     .populate('createdBy', 'name email');

    return sendSuccess(res, 'Assessment updated successfully', updatedAssessment);
  } catch (error) {
    return next(error);
  }
};

// Update assessment status
const updateAssessmentStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const assessment = await Assessment.findById(id).populate({
      path: 'course',
      populate: { path: 'program' }
    });
    
    if (!assessment) {
      return sendNotFound(res, 'Assessment not found');
    }

    // Check access permissions
    const course = assessment.course;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    assessment.status = status;
    assessment.updatedBy = req.user._id;

    if (status === 'Evaluated') {
      assessment.evaluatedBy = req.user._id;
      assessment.evaluatedAt = new Date();
      
      // Update statistics
      await assessment.updateStatistics();
    }

    await assessment.save();

    return sendSuccess(res, 'Assessment status updated successfully', assessment);
  } catch (error) {
    return next(error);
  }
};

// Delete assessment (soft delete)
const deleteAssessment = async (req, res, next) => {
  try {
    const { id } = req.params;

    const assessment = await Assessment.findById(id).populate({
      path: 'course',
      populate: { path: 'program' }
    });
    
    if (!assessment) {
      return sendNotFound(res, 'Assessment not found');
    }

    // Check access permissions
    const course = assessment.course;
    
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Don't allow deletion if assessment has results
    const Result = require('../models/Result');
    const resultCount = await Result.countDocuments({ assessmentId: id });
    if (resultCount > 0) {
      return next(new AppError('Cannot delete assessment with existing results', 400));
    }

    assessment.isActive = false;
    assessment.updatedBy = req.user._id;
    await assessment.save();

    return sendSuccess(res, 'Assessment deleted successfully');
  } catch (error) {
    return next(error);
  }
};

// Get assessments by course
const getAssessmentsByCourse = async (req, res, next) => {
  try {
    const { courseId } = req.params;
    const { status } = req.query;

    // Validate course access
    const course = await Course.findById(courseId).populate('program');
    if (!course) {
      return sendNotFound(res, 'Course not found');
    }

    // Check access permissions
    if (req.user.role === 'FACULTY') {
      const isFaculty = course.facultyIds.includes(req.user._id) || 
                       course.coordinator.toString() === req.user._id.toString();
      if (!isFaculty) {
        return next(new AppError('Access denied - you are not assigned to this course', 403));
      }
    }

    if (req.user.role !== 'ADMIN' && course.program.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const assessments = await Assessment.getByCourse(courseId, status);

    return sendSuccess(res, 'Course assessments retrieved successfully', assessments);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getAssessments,
  getAssessmentById,
  createAssessment,
  updateAssessment,
  updateAssessmentStatus,
  deleteAssessment,
  getAssessmentsByCourse,
  createAssessmentValidation,
  updateAssessmentValidation,
  handleValidationErrors,
};
