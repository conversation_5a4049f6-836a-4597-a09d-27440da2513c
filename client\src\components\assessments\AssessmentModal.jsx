import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const AssessmentModal = ({ isOpen, onClose, onSubmit, assessment, mode, courses }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    courseId: '',
    title: '',
    description: '',
    type: 'Quiz',
    maxMarks: 100,
    weightage: 10,
    date: '',
    duration: 60,
    instructions: '',
    configuration: {
      allowLateSubmission: false,
      showResultsImmediately: false,
      randomizeQuestions: false,
      allowMultipleAttempts: false,
      maxAttempts: 1,
    },
    gradingScheme: {
      passingMarks: 40,
      gradingType: 'Percentage',
      grades: [
        { min: 90, max: 100, grade: 'A+' },
        { min: 80, max: 89, grade: 'A' },
        { min: 70, max: 79, grade: 'B+' },
        { min: 60, max: 69, grade: 'B' },
        { min: 50, max: 59, grade: 'C+' },
        { min: 40, max: 49, grade: 'C' },
        { min: 0, max: 39, grade: 'F' },
      ],
    },
  });

  const assessmentTypes = [
    'T1', 'T2', 'EndSem', 'Quiz', 'Assignment', 'Lab', 'Project', 'Presentation', 'Viva'
  ];

  useEffect(() => {
    if (assessment && mode === 'edit') {
      setFormData({
        courseId: assessment.courseId || '',
        title: assessment.title || '',
        description: assessment.description || '',
        type: assessment.type || 'Quiz',
        maxMarks: assessment.maxMarks || 100,
        weightage: assessment.weightage || 10,
        date: assessment.date ? new Date(assessment.date).toISOString().split('T')[0] : '',
        duration: assessment.duration || 60,
        instructions: assessment.instructions || '',
        configuration: {
          allowLateSubmission: assessment.configuration?.allowLateSubmission || false,
          showResultsImmediately: assessment.configuration?.showResultsImmediately || false,
          randomizeQuestions: assessment.configuration?.randomizeQuestions || false,
          allowMultipleAttempts: assessment.configuration?.allowMultipleAttempts || false,
          maxAttempts: assessment.configuration?.maxAttempts || 1,
        },
        gradingScheme: assessment.gradingScheme || {
          passingMarks: 40,
          gradingType: 'Percentage',
          grades: [
            { min: 90, max: 100, grade: 'A+' },
            { min: 80, max: 89, grade: 'A' },
            { min: 70, max: 79, grade: 'B+' },
            { min: 60, max: 69, grade: 'B' },
            { min: 50, max: 59, grade: 'C+' },
            { min: 40, max: 49, grade: 'C' },
            { min: 0, max: 39, grade: 'F' },
          ],
        },
      });
    } else {
      // Reset form for create mode
      setFormData({
        courseId: '',
        title: '',
        description: '',
        type: 'Quiz',
        maxMarks: 100,
        weightage: 10,
        date: '',
        duration: 60,
        instructions: '',
        configuration: {
          allowLateSubmission: false,
          showResultsImmediately: false,
          randomizeQuestions: false,
          allowMultipleAttempts: false,
          maxAttempts: 1,
        },
        gradingScheme: {
          passingMarks: 40,
          gradingType: 'Percentage',
          grades: [
            { min: 90, max: 100, grade: 'A+' },
            { min: 80, max: 89, grade: 'A' },
            { min: 70, max: 79, grade: 'B+' },
            { min: 60, max: 69, grade: 'B' },
            { min: 50, max: 59, grade: 'C+' },
            { min: 40, max: 49, grade: 'C' },
            { min: 0, max: 39, grade: 'F' },
          ],
        },
      });
    }
  }, [assessment, mode]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : (type === 'number' ? parseFloat(value) || 0 : value),
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : (type === 'number' ? parseFloat(value) || 0 : value),
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.courseId || !formData.title || !formData.date) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.maxMarks <= 0) {
      toast.error('Maximum marks must be greater than 0');
      return;
    }

    if (formData.weightage < 0 || formData.weightage > 100) {
      toast.error('Weightage must be between 0 and 100');
      return;
    }

    if (formData.duration <= 0) {
      toast.error('Duration must be greater than 0');
      return;
    }

    if (new Date(formData.date) < new Date().setHours(0, 0, 0, 0)) {
      toast.error('Assessment date cannot be in the past');
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    {mode === 'create' ? 'Create New Assessment' : 'Edit Assessment'}
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="courseId" className="block text-sm font-medium text-gray-700">
                        Course *
                      </label>
                      <select
                        id="courseId"
                        name="courseId"
                        value={formData.courseId}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="">Select Course</option>
                        {courses.map((course) => (
                          <option key={course._id} value={course._id}>
                            {course.code} - {course.title}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                        Assessment Type *
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        {assessmentTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                        Assessment Title *
                      </label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={3}
                        value={formData.description}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  {/* Assessment Details */}
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
                    <div>
                      <label htmlFor="maxMarks" className="block text-sm font-medium text-gray-700">
                        Maximum Marks *
                      </label>
                      <input
                        type="number"
                        id="maxMarks"
                        name="maxMarks"
                        value={formData.maxMarks}
                        onChange={handleInputChange}
                        required
                        min="1"
                        max="1000"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="weightage" className="block text-sm font-medium text-gray-700">
                        Weightage (%) *
                      </label>
                      <input
                        type="number"
                        id="weightage"
                        name="weightage"
                        value={formData.weightage}
                        onChange={handleInputChange}
                        required
                        min="0"
                        max="100"
                        step="0.1"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="date" className="block text-sm font-medium text-gray-700">
                        Assessment Date *
                      </label>
                      <input
                        type="date"
                        id="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="duration" className="block text-sm font-medium text-gray-700">
                        Duration (minutes) *
                      </label>
                      <input
                        type="number"
                        id="duration"
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        required
                        min="1"
                        max="600"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  {/* Instructions */}
                  <div>
                    <label htmlFor="instructions" className="block text-sm font-medium text-gray-700">
                      Instructions for Students
                    </label>
                    <textarea
                      id="instructions"
                      name="instructions"
                      rows={4}
                      value={formData.instructions}
                      onChange={handleInputChange}
                      placeholder="Enter any special instructions for students..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  {/* Configuration */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Assessment Configuration</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="flex items-center">
                        <input
                          id="configuration.allowLateSubmission"
                          name="configuration.allowLateSubmission"
                          type="checkbox"
                          checked={formData.configuration.allowLateSubmission}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="configuration.allowLateSubmission" className="ml-2 block text-sm text-gray-900">
                          Allow late submission
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          id="configuration.showResultsImmediately"
                          name="configuration.showResultsImmediately"
                          type="checkbox"
                          checked={formData.configuration.showResultsImmediately}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="configuration.showResultsImmediately" className="ml-2 block text-sm text-gray-900">
                          Show results immediately
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          id="configuration.randomizeQuestions"
                          name="configuration.randomizeQuestions"
                          type="checkbox"
                          checked={formData.configuration.randomizeQuestions}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="configuration.randomizeQuestions" className="ml-2 block text-sm text-gray-900">
                          Randomize question order
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          id="configuration.allowMultipleAttempts"
                          name="configuration.allowMultipleAttempts"
                          type="checkbox"
                          checked={formData.configuration.allowMultipleAttempts}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="configuration.allowMultipleAttempts" className="ml-2 block text-sm text-gray-900">
                          Allow multiple attempts
                        </label>
                      </div>
                    </div>

                    {formData.configuration.allowMultipleAttempts && (
                      <div className="mt-4">
                        <label htmlFor="configuration.maxAttempts" className="block text-sm font-medium text-gray-700">
                          Maximum Attempts
                        </label>
                        <input
                          type="number"
                          id="configuration.maxAttempts"
                          name="configuration.maxAttempts"
                          value={formData.configuration.maxAttempts}
                          onChange={handleInputChange}
                          min="1"
                          max="10"
                          className="mt-1 block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                    )}
                  </div>

                  {/* Grading Scheme */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Grading Scheme</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="gradingScheme.passingMarks" className="block text-sm font-medium text-gray-700">
                          Passing Marks (%)
                        </label>
                        <input
                          type="number"
                          id="gradingScheme.passingMarks"
                          name="gradingScheme.passingMarks"
                          value={formData.gradingScheme.passingMarks}
                          onChange={handleInputChange}
                          min="0"
                          max="100"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>

                      <div>
                        <label htmlFor="gradingScheme.gradingType" className="block text-sm font-medium text-gray-700">
                          Grading Type
                        </label>
                        <select
                          id="gradingScheme.gradingType"
                          name="gradingScheme.gradingType"
                          value={formData.gradingScheme.gradingType}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="Percentage">Percentage</option>
                          <option value="Letter">Letter Grade</option>
                          <option value="Points">Points</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : mode === 'create' ? 'Create Assessment' : 'Update Assessment'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
