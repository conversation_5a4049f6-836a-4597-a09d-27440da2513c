const mongoose = require('mongoose');

const CourseSchema = new mongoose.Schema({
  programId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Program',
    required: [true, 'Program is required'],
  },
  code: {
    type: String,
    required: [true, 'Course code is required'],
    uppercase: true,
    trim: true,
    maxlength: [20, 'Course code cannot exceed 20 characters'],
    match: [/^[A-Z]{2,4}\d{3,4}[A-Z]?$/, 'Course code format is invalid (e.g., CSE101, MATH201A)'],
  },
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true,
    maxlength: [200, 'Course title cannot exceed 200 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Course description cannot exceed 1000 characters'],
  },
  semester: {
    type: Number,
    required: [true, 'Semester is required'],
    min: [1, 'Semester must be at least 1'],
    max: [20, 'Semester cannot exceed 20'],
  },
  year: {
    type: Number,
    required: [true, 'Academic year is required'],
    min: [2000, 'Year must be after 2000'],
    max: [2100, 'Year cannot exceed 2100'],
  },
  credits: {
    theory: {
      type: Number,
      default: 0,
      min: [0, 'Theory credits cannot be negative'],
    },
    practical: {
      type: Number,
      default: 0,
      min: [0, 'Practical credits cannot be negative'],
    },
    total: {
      type: Number,
      required: [true, 'Total credits is required'],
      min: [1, 'Total credits must be at least 1'],
    },
  },
  type: {
    type: String,
    enum: {
      values: ['Core', 'Elective', 'Open Elective', 'Project', 'Internship', 'Seminar'],
      message: 'Course type must be one of: Core, Elective, Open Elective, Project, Internship, Seminar',
    },
    required: [true, 'Course type is required'],
  },
  category: {
    type: String,
    enum: {
      values: ['Theory', 'Practical', 'Theory + Practical'],
      message: 'Course category must be one of: Theory, Practical, Theory + Practical',
    },
    required: [true, 'Course category is required'],
  },
  // Faculty assignments
  facultyIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  }],
  coordinator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Course coordinator is required'],
  },
  // Course Outcomes (COs)
  cos: [{
    index: {
      type: Number,
      required: true,
      min: [1, 'CO index must be at least 1'],
    },
    statement: {
      type: String,
      required: [true, 'CO statement is required'],
      trim: true,
      maxlength: [500, 'CO statement cannot exceed 500 characters'],
    },
    target: {
      type: Number,
      required: [true, 'CO target attainment is required'],
      min: [0, 'Target cannot be negative'],
      max: [100, 'Target cannot exceed 100%'],
      default: 70,
    },
    bloomsLevel: {
      type: String,
      enum: ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'],
      required: [true, 'Blooms taxonomy level is required'],
    },
  }],
  // CO-PO/PSO mapping with level (1/2/3) and weight (0..1)
  mapping: {
    po: [{
      coIndex: {
        type: Number,
        required: true,
        min: [1, 'CO index must be at least 1'],
      },
      poCode: {
        type: String,
        required: true,
        trim: true,
      },
      level: {
        type: Number,
        enum: {
          values: [1, 2, 3],
          message: 'Mapping level must be 1, 2, or 3',
        },
        required: true,
      },
      weight: {
        type: Number,
        required: true,
        min: [0, 'Weight cannot be negative'],
        max: [1, 'Weight cannot exceed 1'],
      },
    }],
    pso: [{
      coIndex: {
        type: Number,
        required: true,
        min: [1, 'CO index must be at least 1'],
      },
      psoCode: {
        type: String,
        required: true,
        trim: true,
      },
      level: {
        type: Number,
        enum: {
          values: [1, 2, 3],
          message: 'Mapping level must be 1, 2, or 3',
        },
        required: true,
      },
      weight: {
        type: Number,
        required: true,
        min: [0, 'Weight cannot be negative'],
        max: [1, 'Weight cannot exceed 1'],
      },
    }],
  },
  // Course schedule
  schedule: {
    startDate: {
      type: Date,
      required: [true, 'Course start date is required'],
    },
    endDate: {
      type: Date,
      required: [true, 'Course end date is required'],
      validate: {
        validator: function(value) {
          return value > this.schedule.startDate;
        },
        message: 'End date must be after start date',
      },
    },
    classesPerWeek: {
      type: Number,
      default: 3,
      min: [1, 'Classes per week must be at least 1'],
      max: [10, 'Classes per week cannot exceed 10'],
    },
  },
  // Enrollment details
  enrollment: {
    maxStudents: {
      type: Number,
      required: [true, 'Maximum students is required'],
      min: [1, 'Maximum students must be at least 1'],
    },
    enrolledStudents: {
      type: Number,
      default: 0,
      min: [0, 'Enrolled students cannot be negative'],
    },
  },
  // Course materials and resources
  syllabus: {
    objectives: [String],
    topics: [{
      unit: Number,
      title: String,
      content: String,
      hours: Number,
    }],
    textbooks: [{
      title: String,
      author: String,
      publisher: String,
      year: Number,
    }],
    references: [{
      title: String,
      author: String,
      publisher: String,
      year: Number,
    }],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes
CourseSchema.index({ programId: 1, semester: 1, year: 1 });
CourseSchema.index({ code: 1, year: 1 });
CourseSchema.index({ facultyIds: 1 });
CourseSchema.index({ coordinator: 1 });
CourseSchema.index({ isActive: 1 });

// Virtual for program details
CourseSchema.virtual('program', {
  ref: 'Program',
  localField: 'programId',
  foreignField: '_id',
  justOne: true,
});

// Virtual for faculty details
CourseSchema.virtual('faculty', {
  ref: 'User',
  localField: 'facultyIds',
  foreignField: '_id',
});

// Virtual for coordinator details
CourseSchema.virtual('coordinatorDetails', {
  ref: 'User',
  localField: 'coordinator',
  foreignField: '_id',
  justOne: true,
});

// Virtual for assessments count
CourseSchema.virtual('assessmentsCount', {
  ref: 'Assessment',
  localField: '_id',
  foreignField: 'courseId',
  count: true,
});

// Pre-save middleware to validate credits
CourseSchema.pre('save', function(next) {
  if (this.credits.theory + this.credits.practical !== this.credits.total) {
    return next(new Error('Total credits must equal sum of theory and practical credits'));
  }
  next();
});

// Static method to find by code and year
CourseSchema.statics.findByCodeAndYear = function(code, year) {
  return this.findOne({ code: code.toUpperCase(), year });
};

// Static method to get courses by program and semester
CourseSchema.statics.getByProgramAndSemester = function(programId, semester, year) {
  return this.find({ programId, semester, year, isActive: true })
    .populate('faculty', 'name email')
    .populate('coordinatorDetails', 'name email');
};

// Static method to get courses by faculty
CourseSchema.statics.getByFaculty = function(facultyId, year) {
  return this.find({ 
    $or: [
      { facultyIds: facultyId },
      { coordinator: facultyId }
    ],
    year,
    isActive: true 
  }).populate('program', 'name code');
};

// Instance method to validate CO-PO mapping
CourseSchema.methods.validateMapping = async function() {
  const program = await mongoose.model('Program').findById(this.programId);
  if (!program) return { isValid: false, errors: ['Program not found'] };

  const errors = [];
  const poCodes = program.pos.map(po => po.code);
  const psoCodes = program.psos.map(pso => pso.code);
  const coCodes = this.cos.map(co => co.index);

  // Validate PO mappings
  this.mapping.po.forEach(mapping => {
    if (!coCodes.includes(mapping.coIndex)) {
      errors.push(`CO${mapping.coIndex} not found in course COs`);
    }
    if (!poCodes.includes(mapping.poCode)) {
      errors.push(`PO ${mapping.poCode} not found in program POs`);
    }
  });

  // Validate PSO mappings
  this.mapping.pso.forEach(mapping => {
    if (!coCodes.includes(mapping.coIndex)) {
      errors.push(`CO${mapping.coIndex} not found in course COs`);
    }
    if (!psoCodes.includes(mapping.psoCode)) {
      errors.push(`PSO ${mapping.psoCode} not found in program PSOs`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Instance method to get course statistics
CourseSchema.methods.getStatistics = async function() {
  const Assessment = mongoose.model('Assessment');
  
  const assessmentsCount = await Assessment.countDocuments({ courseId: this._id });

  return {
    assessmentsCount,
    totalCOs: this.cos.length,
    poMappings: this.mapping.po.length,
    psoMappings: this.mapping.pso.length,
    enrolledStudents: this.enrollment.enrolledStudents,
    maxStudents: this.enrollment.maxStudents,
    enrollmentPercentage: (this.enrollment.enrolledStudents / this.enrollment.maxStudents) * 100,
  };
};

module.exports = mongoose.model('Course', CourseSchema);
