import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const QuestionMappingModal = ({ isOpen, onClose, onSubmit, assessment }) => {
  const [loading, setLoading] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [coTags, setCoTags] = useState([]);

  const bloomsLevels = [
    'Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'
  ];

  const difficultyLevels = ['Easy', 'Medium', 'Hard'];

  useEffect(() => {
    if (assessment) {
      setQuestions(assessment.questions || []);
      setCoTags(assessment.coTags || []);
    }
  }, [assessment]);

  const addQuestion = () => {
    const newQuestion = {
      questionNumber: questions.length + 1,
      marks: 0,
      coIndex: 1,
      bloomsLevel: 'Remember',
      difficulty: 'Medium',
      topic: '',
    };
    setQuestions([...questions, newQuestion]);
  };

  const removeQuestion = (index) => {
    const updatedQuestions = questions.filter((_, i) => i !== index);
    // Renumber questions
    const renumberedQuestions = updatedQuestions.map((q, i) => ({
      ...q,
      questionNumber: i + 1,
    }));
    setQuestions(renumberedQuestions);
    updateCoTags(renumberedQuestions);
  };

  const updateQuestion = (index, field, value) => {
    const updatedQuestions = questions.map((q, i) => {
      if (i === index) {
        return {
          ...q,
          [field]: field === 'marks' || field === 'coIndex' || field === 'questionNumber' 
            ? parseInt(value) || 0 
            : value,
        };
      }
      return q;
    });
    setQuestions(updatedQuestions);
    
    if (field === 'marks' || field === 'coIndex') {
      updateCoTags(updatedQuestions);
    }
  };

  const updateCoTags = (questionsData) => {
    // Calculate CO-wise marks distribution
    const coMarksMap = {};
    
    questionsData.forEach(q => {
      if (!coMarksMap[q.coIndex]) {
        coMarksMap[q.coIndex] = 0;
      }
      coMarksMap[q.coIndex] += q.marks;
    });

    const totalMarks = Object.values(coMarksMap).reduce((sum, marks) => sum + marks, 0);
    
    const newCoTags = Object.entries(coMarksMap).map(([coIndex, marks]) => ({
      coIndex: parseInt(coIndex),
      marks,
      weight: totalMarks > 0 ? marks / totalMarks : 0,
    }));

    setCoTags(newCoTags);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (questions.length === 0) {
      toast.error('Please add at least one question');
      return;
    }

    for (let i = 0; i < questions.length; i++) {
      const q = questions[i];
      if (q.marks <= 0) {
        toast.error(`Question ${i + 1} must have marks greater than 0`);
        return;
      }
      if (!q.coIndex || q.coIndex < 1) {
        toast.error(`Question ${i + 1} must be mapped to a valid CO`);
        return;
      }
    }

    const totalMarks = questions.reduce((sum, q) => sum + q.marks, 0);
    if (totalMarks !== assessment.maxMarks) {
      toast.error(`Total question marks (${totalMarks}) must equal assessment max marks (${assessment.maxMarks})`);
      return;
    }

    setLoading(true);
    try {
      await onSubmit(questions, coTags);
    } catch (error) {
      console.error('Error submitting questions:', error);
    } finally {
      setLoading(false);
    }
  };

  const totalQuestionMarks = questions.reduce((sum, q) => sum + q.marks, 0);
  const courseCOs = assessment?.course?.cos || [];

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-7xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Question Mapping & CO Analysis
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      {assessment?.title} - {assessment?.course?.code}
                    </p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {courseCOs.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      No Course Outcomes defined for this course. Please define COs first.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Assessment Summary */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                        <div>
                          <span className="text-sm font-medium text-gray-500">Max Marks</span>
                          <p className="text-lg font-semibold text-gray-900">{assessment?.maxMarks}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">Question Marks</span>
                          <p className={`text-lg font-semibold ${
                            totalQuestionMarks === assessment?.maxMarks 
                              ? 'text-green-600' 
                              : 'text-red-600'
                          }`}>
                            {totalQuestionMarks}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">Questions</span>
                          <p className="text-lg font-semibold text-gray-900">{questions.length}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">Course COs</span>
                          <p className="text-lg font-semibold text-gray-900">{courseCOs.length}</p>
                        </div>
                      </div>
                    </div>

                    {/* Questions List */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-gray-900">Questions</h4>
                        <button
                          type="button"
                          onClick={addQuestion}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <PlusIcon className="-ml-0.5 mr-2 h-4 w-4" />
                          Add Question
                        </button>
                      </div>

                      {questions.length === 0 ? (
                        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                          <p className="text-gray-500">No questions added yet.</p>
                          <button
                            type="button"
                            onClick={addQuestion}
                            className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                            Add First Question
                          </button>
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Q#
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Marks
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  CO
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Bloom's Level
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Difficulty
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Topic
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {questions.map((question, index) => (
                                <tr key={index}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {question.questionNumber}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <input
                                      type="number"
                                      value={question.marks}
                                      onChange={(e) => updateQuestion(index, 'marks', e.target.value)}
                                      min="0"
                                      max={assessment?.maxMarks}
                                      className="block w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                    />
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <select
                                      value={question.coIndex}
                                      onChange={(e) => updateQuestion(index, 'coIndex', e.target.value)}
                                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                    >
                                      {courseCOs.map((co) => (
                                        <option key={co.index} value={co.index}>
                                          CO{co.index}
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <select
                                      value={question.bloomsLevel}
                                      onChange={(e) => updateQuestion(index, 'bloomsLevel', e.target.value)}
                                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                    >
                                      {bloomsLevels.map((level) => (
                                        <option key={level} value={level}>
                                          {level}
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <select
                                      value={question.difficulty}
                                      onChange={(e) => updateQuestion(index, 'difficulty', e.target.value)}
                                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                    >
                                      {difficultyLevels.map((level) => (
                                        <option key={level} value={level}>
                                          {level}
                                        </option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <input
                                      type="text"
                                      value={question.topic}
                                      onChange={(e) => updateQuestion(index, 'topic', e.target.value)}
                                      placeholder="Topic/Unit"
                                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                    />
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button
                                      type="button"
                                      onClick={() => removeQuestion(index)}
                                      className="text-red-600 hover:text-red-900"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </div>

                    {/* CO Distribution */}
                    {coTags.length > 0 && (
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 mb-4">CO-wise Marks Distribution</h4>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                          {coTags.map((tag) => {
                            const co = courseCOs.find(c => c.index === tag.coIndex);
                            return (
                              <div key={tag.coIndex} className="bg-blue-50 rounded-lg p-4">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-blue-900">
                                    CO{tag.coIndex}
                                  </span>
                                  <span className="text-sm font-semibold text-blue-900">
                                    {tag.marks} marks ({(tag.weight * 100).toFixed(1)}%)
                                  </span>
                                </div>
                                {co && (
                                  <p className="text-xs text-blue-700 truncate" title={co.statement}>
                                    {co.statement}
                                  </p>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* Validation Messages */}
                    {totalQuestionMarks !== assessment?.maxMarks && (
                      <div className="bg-red-50 border border-red-200 rounded-md p-4">
                        <p className="text-sm text-red-800">
                          <strong>Warning:</strong> Total question marks ({totalQuestionMarks}) 
                          must equal assessment max marks ({assessment?.maxMarks}).
                        </p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={onClose}
                        className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading || questions.length === 0 || totalQuestionMarks !== assessment?.maxMarks}
                        className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? 'Saving...' : 'Save Questions'}
                      </button>
                    </div>
                  </form>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
