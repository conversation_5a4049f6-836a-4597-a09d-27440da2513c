const express = require('express');
const {
  getProgramReadiness,
  getReadinessMetrics,
  getReadinessMetricById,
  createReadinessMetric,
  updateReadinessMetric,
  updateMetricValue,
  addActionItem,
  getReadinessDashboard,
  getReadinessTrends,
  createMetricValidation,
  handleValidationErrors,
} = require('../controllers/readinessController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get program readiness overview
router.get('/program/:programId', getProgramReadiness);

// Get readiness dashboard data
router.get('/program/:programId/dashboard', getReadinessDashboard);

// Get readiness trends
router.get('/program/:programId/trends', getReadinessTrends);

// Get all readiness metrics for a program
router.get('/program/:programId/metrics', getReadinessMetrics);

// Get specific readiness metric
router.get('/metric/:id', getReadinessMetricById);

// Create new readiness metric (Admin and Dept Head only)
router.post('/metric', authorize('ADMIN', 'DEPT_HEAD'), createMetricValidation, handleValidationErrors, createReadinessMetric);

// Update readiness metric (Admin and Dept Head only)
router.put('/metric/:id', authorize('ADMIN', 'DEPT_HEAD'), updateReadinessMetric);

// Update metric value (Admin and Dept Head only)
router.patch('/metric/:id/value', authorize('ADMIN', 'DEPT_HEAD'), updateMetricValue);

// Add action item to metric (Admin and Dept Head only)
router.post('/metric/:id/action-item', authorize('ADMIN', 'DEPT_HEAD'), addActionItem);

module.exports = router;
