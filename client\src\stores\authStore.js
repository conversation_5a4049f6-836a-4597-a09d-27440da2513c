import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';
import toast from 'react-hot-toast';

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // ----------------------------
      // State
      // ----------------------------
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,

      // ----------------------------
      // Actions
      // ----------------------------
      login: async (credentials) => {
        try {
          console.log('AuthStore: Starting login...', credentials.email);
          set({ isLoading: true });

          const response = await authAPI.login(credentials);
          console.log('AuthStore: Login response received:', response.data);
          console.log('AuthStore: Full response structure:', response);

          // The response structure is: response.data.data contains { user, token, refreshToken }
          const responseData = response.data.data || response.data;
          const { user, token, refreshToken } = responseData;

          console.log('AuthStore: Extracted data:', { user, token: !!token, refreshToken: !!refreshToken });

          if (!user || !token) {
            throw new Error('Invalid response: missing user or token');
          }

          if (!user.name) {
            console.warn('AuthStore: User object missing name field:', user);
          }

          const newState = {
            user,
            token,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
          };

          set(newState);

          console.log('AuthStore: Login successful, state updated');
          console.log('AuthStore: New state:', newState);

          // Verify the state was actually set
          const currentState = get();
          console.log('AuthStore: Current state after set:', {
            user: currentState.user,
            isAuthenticated: currentState.isAuthenticated,
            isLoading: currentState.isLoading,
            token: currentState.token ? 'Token present' : 'No token'
          });

          // Check localStorage
          const stored = localStorage.getItem('auth-storage');
          console.log('AuthStore: localStorage content:', stored);
          toast.success(`Welcome back, ${user.name || user.email || 'User'}!`);
          return { success: true };
        } catch (error) {
          console.error('AuthStore: Login failed:', error);
          set({ isLoading: false });
          const message = error.response?.data?.message || 'Login failed';
          toast.error(message);
          return { success: false, error: message };
        }
      },

      logout: async () => {
        try {
          const { token } = get();
          if (token) {
            await authAPI.logout();
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
          });
          toast.success('Logged out successfully');
        }
      },

      refreshAuth: async () => {
        try {
          const { refreshToken } = get();
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          const response = await authAPI.refreshToken({ refreshToken });
          const { token: newToken, refreshToken: newRefreshToken } = response.data;

          set({
            token: newToken,
            refreshToken: newRefreshToken,
          });

          return { success: true };
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().logout();
          return { success: false };
        }
      },

      updateProfile: async (profileData) => {
        try {
          set({ isLoading: true });
          
          const response = await authAPI.updateProfile(profileData);
          const updatedUser = response.data;

          set({
            user: updatedUser,
            isLoading: false,
          });

          toast.success('Profile updated successfully');
          return { success: true };
        } catch (error) {
          set({ isLoading: false });
          const message = error.response?.data?.message || 'Profile update failed';
          toast.error(message);
          return { success: false, error: message };
        }
      },

      changePassword: async (passwordData) => {
        try {
          set({ isLoading: true });
          
          await authAPI.changePassword(passwordData);

          set({ isLoading: false });
          toast.success('Password changed successfully');
          return { success: true };
        } catch (error) {
          set({ isLoading: false });
          const message = error.response?.data?.message || 'Password change failed';
          toast.error(message);
          return { success: false, error: message };
        }
      },

      // ----------------------------
      // 🔑 Check Auth on app load
      // ----------------------------
      checkAuth: async () => {
        try {
          console.log('AuthStore: Starting auth check...');
          set({ isLoading: true });
          const { token } = get();

          if (!token) {
            console.log('AuthStore: No token found, setting unauthenticated state');
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
            });
            return { success: false };
          }

          console.log('AuthStore: Token found, verifying with server...');
          const response = await authAPI.getProfile();
          console.log('AuthStore: getProfile response:', response);

          // Handle both response structures: response.data.data or response.data
          const user = response.data.data || response.data;

          console.log('AuthStore: Auth check successful, user:', user?.name || 'No name');
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          });

          return { success: true };
        } catch (error) {
          console.error("AuthStore: Auth check failed:", error);
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
          });
          return { success: false };
        }
      },

      // ----------------------------
      // Utility functions (role checks)
      // ----------------------------
      hasRole: (role) => {
        const { user } = get();
        return user?.role === role;
      },

      hasAnyRole: (roles) => {
        const { user } = get();
        return roles.includes(user?.role);
      },

      isAdmin: () => {
        const { user } = get();
        return user?.role === 'ADMIN';
      },

      isDeptHead: () => {
        const { user } = get();
        return user?.role === 'DEPT_HEAD';
      },

      isFaculty: () => {
        const { user } = get();
        return user?.role === 'FACULTY';
      },

      isAuditor: () => {
        const { user } = get();
        return user?.role === 'AUDITOR';
      },

      canManageUsers: () => {
        const { user } = get();
        return ['ADMIN', 'DEPT_HEAD'].includes(user?.role);
      },

      canManageDepartments: () => {
        const { user } = get();
        return user?.role === 'ADMIN';
      },

      canManagePrograms: () => {
        const { user } = get();
        return ['ADMIN', 'DEPT_HEAD'].includes(user?.role);
      },

      canManageCourses: () => {
        const { user } = get();
        return ['ADMIN', 'DEPT_HEAD', 'FACULTY'].includes(user?.role);
      },

      canViewReports: () => {
        const { user } = get();
        return user?.role !== undefined; // All authenticated users can view reports
      },

      // Clear store (for testing or manual reset)
      clearStore: () => {
        // Clear localStorage
        localStorage.removeItem('auth-storage');
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        console.log('AuthStore: Rehydrating from localStorage:', state);
      },
    }
  )
);

// ----------------------------
// Auto-refresh token setup
// ----------------------------
let refreshTimer;

export const setupTokenRefresh = () => {
  const { token, refreshAuth, logout } = useAuthStore.getState();
  
  if (!token) return;

  // Clear existing timer
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }

  try {
    // Decode JWT to get expiration time
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiry = expirationTime - currentTime;
    
    // Refresh token 5 minutes before expiry
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0);

    if (refreshTime > 0) {
      refreshTimer = setTimeout(async () => {
        const result = await refreshAuth();
        if (result.success) {
          setupTokenRefresh(); // Setup next refresh
        } else {
          logout(); // Force logout if refresh fails
        }
      }, refreshTime);
    } else {
      // Token is already expired or about to expire, try to refresh immediately
      refreshAuth().then((result) => {
        if (result.success) {
          setupTokenRefresh();
        } else {
          logout();
        }
      });
    }
  } catch (error) {
    console.error('Error setting up token refresh:', error);
    logout();
  }
};

// Initialize token refresh on store creation
if (useAuthStore.getState().token) {
  setupTokenRefresh();
}

// Subscribe to token changes to setup refresh
useAuthStore.subscribe(
  (state) => state.token,
  (token) => {
    if (token) {
      setupTokenRefresh();
    } else if (refreshTimer) {
      clearTimeout(refreshTimer);
      refreshTimer = null;
    }
  }
);
