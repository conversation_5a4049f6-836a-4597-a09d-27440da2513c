const { body } = require('express-validator');
const Department = require('../models/Department');
const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendCreated, sendNotFound } = require('../utils/response');

// Validation rules
const createDepartmentValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Department name must be between 2 and 100 characters'),
  body('code')
    .trim()
    .isLength({ min: 2, max: 10 })
    .matches(/^[A-Z]+$/)
    .withMessage('Department code must be 2-10 uppercase letters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('establishedYear')
    .optional()
    .isInt({ min: 1900, max: new Date().getFullYear() })
    .withMessage('Established year must be between 1900 and current year'),
];

const updateDepartmentValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Department name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('establishedYear')
    .optional()
    .isInt({ min: 1900, max: new Date().getFullYear() })
    .withMessage('Established year must be between 1900 and current year'),
];

// Get all departments
const getDepartments = async (req, res, next) => {
  try {
    const { isActive } = req.query;
    
    const filter = {};
    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    const departments = await Department.find(filter)
      .populate('headOfDepartment', 'name email')
      .sort({ name: 1 });

    // Get statistics for each department
    const departmentsWithStats = await Promise.all(
      departments.map(async (dept) => {
        const stats = await dept.getStatistics();
        return {
          ...dept.toObject(),
          statistics: stats,
        };
      })
    );

    return sendSuccess(res, 'Departments retrieved successfully', departmentsWithStats);
  } catch (error) {
    return next(error);
  }
};

// Get department by ID
const getDepartmentById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const department = await Department.findById(id)
      .populate('headOfDepartment', 'name email phone designation');

    if (!department) {
      return sendNotFound(res, 'Department not found');
    }

    const statistics = await department.getStatistics();

    return sendSuccess(res, 'Department retrieved successfully', {
      ...department.toObject(),
      statistics,
    });
  } catch (error) {
    return next(error);
  }
};

// Create new department (Admin only)
const createDepartment = async (req, res, next) => {
  try {
    const { name, code, description, establishedYear, contactInfo } = req.body;

    // Check if department code already exists
    const existingDept = await Department.findByCode(code);
    if (existingDept) {
      return next(new AppError('Department with this code already exists', 409));
    }

    const department = new Department({
      name,
      code: code.toUpperCase(),
      description,
      establishedYear,
      contactInfo,
      createdBy: req.user._id,
    });

    await department.save();

    return sendCreated(res, 'Department created successfully', department);
  } catch (error) {
    return next(error);
  }
};

// Update department (Admin only)
const updateDepartment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, establishedYear, contactInfo, headOfDepartment } = req.body;

    const department = await Department.findById(id);
    if (!department) {
      return sendNotFound(res, 'Department not found');
    }

    // Validate head of department if provided
    if (headOfDepartment) {
      const user = await User.findById(headOfDepartment);
      if (!user || user.deptId.toString() !== id || !['DEPT_HEAD', 'FACULTY'].includes(user.role)) {
        return next(new AppError('Invalid head of department', 400));
      }
    }

    const updatedDepartment = await Department.findByIdAndUpdate(
      id,
      {
        name,
        description,
        establishedYear,
        contactInfo,
        headOfDepartment,
        updatedBy: req.user._id,
      },
      { new: true, runValidators: true }
    ).populate('headOfDepartment', 'name email');

    return sendSuccess(res, 'Department updated successfully', updatedDepartment);
  } catch (error) {
    return next(error);
  }
};

// Deactivate department (Admin only)
const deactivateDepartment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if department has active users
    const activeUsers = await User.countDocuments({ deptId: id, isActive: true });
    if (activeUsers > 0) {
      return next(new AppError('Cannot deactivate department with active users', 400));
    }

    const department = await Department.findByIdAndUpdate(
      id,
      { isActive: false, updatedBy: req.user._id },
      { new: true }
    );

    if (!department) {
      return sendNotFound(res, 'Department not found');
    }

    return sendSuccess(res, 'Department deactivated successfully', department);
  } catch (error) {
    return next(error);
  }
};

// Activate department (Admin only)
const activateDepartment = async (req, res, next) => {
  try {
    const { id } = req.params;

    const department = await Department.findByIdAndUpdate(
      id,
      { isActive: true, updatedBy: req.user._id },
      { new: true }
    );

    if (!department) {
      return sendNotFound(res, 'Department not found');
    }

    return sendSuccess(res, 'Department activated successfully', department);
  } catch (error) {
    return next(error);
  }
};

// Get department faculty
const getDepartmentFaculty = async (req, res, next) => {
  try {
    const { id } = req.params;

    const department = await Department.findById(id);
    if (!department) {
      return sendNotFound(res, 'Department not found');
    }

    const faculty = await User.find({ deptId: id, isActive: true })
      .select('name email role phone designation qualifications experience')
      .sort({ name: 1 });

    return sendSuccess(res, 'Department faculty retrieved successfully', faculty);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deactivateDepartment,
  activateDepartment,
  getDepartmentFaculty,
  createDepartmentValidation,
  updateDepartmentValidation,
  handleValidationErrors,
};
