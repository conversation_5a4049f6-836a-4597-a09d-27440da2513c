import { useState } from 'react';
import {
  BookOpenIcon,
  AcademicCapIcon,
  UserGroupIcon,
  CalendarIcon,
  PencilIcon,
  CogIcon,
  ChartBarIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const CourseCard = ({ course, onEdit, onManageCOs, onManageMapping }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getTypeColor = (type) => {
    const colors = {
      'Core': 'bg-blue-100 text-blue-800',
      'Elective': 'bg-green-100 text-green-800',
      'Open Elective': 'bg-purple-100 text-purple-800',
      'Project': 'bg-orange-100 text-orange-800',
      'Internship': 'bg-pink-100 text-pink-800',
      'Seminar': 'bg-indigo-100 text-indigo-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Theory': 'bg-blue-50 text-blue-700 border-blue-200',
      'Practical': 'bg-green-50 text-green-700 border-green-200',
      'Theory + Practical': 'bg-purple-50 text-purple-700 border-purple-200',
    };
    return colors[category] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  const enrollmentPercentage = course.enrollment?.maxStudents 
    ? (course.enrollment.enrolledStudents / course.enrollment.maxStudents) * 100 
    : 0;

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-500">{course.code}</span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(course.type)}`}>
                {course.type}
              </span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 truncate" title={course.title}>
              {course.title}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {course.program?.name} • Semester {course.semester} • {course.year}
            </p>
          </div>
          
          {/* Actions Menu */}
          <Menu as="div" className="relative inline-block text-left">
            <div>
              <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1">
                <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
              </Menu.Button>
            </div>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <div className="py-1">
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onEdit(course)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <PencilIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        Edit Course
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onManageCOs(course)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <CogIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        Manage COs
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onManageMapping(course)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <ChartBarIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        CO-PO Mapping
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        {/* Course Details */}
        <div className="mt-4 space-y-3">
          {/* Category and Credits */}
          <div className="flex items-center justify-between">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium border ${getCategoryColor(course.category)}`}>
              {course.category}
            </span>
            <span className="text-sm text-gray-500">
              {course.credits?.total || 0} Credits
            </span>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <UserGroupIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {course.enrollment?.enrolledStudents || 0}/{course.enrollment?.maxStudents || 0}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <AcademicCapIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {course.cos?.length || 0} COs
              </span>
            </div>
          </div>

          {/* Enrollment Progress */}
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Enrollment</span>
              <span>{enrollmentPercentage.toFixed(0)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  enrollmentPercentage >= 90 ? 'bg-red-500' :
                  enrollmentPercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(100, enrollmentPercentage)}%` }}
              ></div>
            </div>
          </div>

          {/* Faculty */}
          {course.coordinatorDetails && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Coordinator:</span>
              <span className="text-sm font-medium text-gray-900">
                {course.coordinatorDetails.name}
              </span>
            </div>
          )}

          {/* Schedule */}
          {course.schedule && (
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {new Date(course.schedule.startDate).toLocaleDateString()} - {new Date(course.schedule.endDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {/* Expandable Description */}
        {course.description && (
          <div className="mt-4">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none"
            >
              {isExpanded ? 'Show less' : 'Show description'}
            </button>
            {isExpanded && (
              <p className="mt-2 text-sm text-gray-600 leading-relaxed">
                {course.description}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">
            Updated {new Date(course.updatedAt).toLocaleDateString()}
          </span>
          <div className="flex space-x-2">
            <button
              onClick={() => onManageCOs(course)}
              className="text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              Manage COs
            </button>
            <span className="text-gray-300">•</span>
            <button
              onClick={() => onManageMapping(course)}
              className="text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              CO-PO Mapping
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
