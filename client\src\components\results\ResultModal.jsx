import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const ResultModal = ({ isOpen, onClose, onSubmit, result, mode, assessments }) => {
  const [loading, setLoading] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [formData, setFormData] = useState({
    assessmentId: '',
    studentRoll: '',
    studentName: '',
    obtained: 0,
    status: 'Pass',
    grade: '',
    evaluationDate: '',
    remarks: '',
    questionWise: [],
  });

  const resultStatuses = ['Pass', 'Fail', 'Absent', 'Malpractice'];

  useEffect(() => {
    if (result && mode === 'edit') {
      setFormData({
        assessmentId: result.assessmentId || '',
        studentRoll: result.studentRoll || '',
        studentName: result.studentName || '',
        obtained: result.obtained || 0,
        status: result.status || 'Pass',
        grade: result.grade || '',
        evaluationDate: result.evaluationDate 
          ? new Date(result.evaluationDate).toISOString().split('T')[0] 
          : '',
        remarks: result.remarks || '',
        questionWise: result.questionWise || [],
      });
      
      // Find and set selected assessment
      const assessment = assessments.find(a => a._id === result.assessmentId);
      setSelectedAssessment(assessment);
    } else {
      // Reset form for create mode
      setFormData({
        assessmentId: '',
        studentRoll: '',
        studentName: '',
        obtained: 0,
        status: 'Pass',
        grade: '',
        evaluationDate: new Date().toISOString().split('T')[0],
        remarks: '',
        questionWise: [],
      });
      setSelectedAssessment(null);
    }
  }, [result, mode, assessments]);

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    }));
  };

  const handleAssessmentChange = (e) => {
    const assessmentId = e.target.value;
    const assessment = assessments.find(a => a._id === assessmentId);
    
    setSelectedAssessment(assessment);
    setFormData(prev => ({
      ...prev,
      assessmentId,
      questionWise: assessment?.questions?.map(q => ({
        q: q.questionNumber,
        coIndex: q.coIndex,
        maxMarks: q.marks,
        marks: 0,
      })) || [],
    }));
  };

  const handleQuestionMarksChange = (questionIndex, marks) => {
    const updatedQuestionWise = formData.questionWise.map((q, index) => {
      if (index === questionIndex) {
        return {
          ...q,
          marks: Math.min(parseFloat(marks) || 0, q.maxMarks),
        };
      }
      return q;
    });

    const totalObtained = updatedQuestionWise.reduce((sum, q) => sum + q.marks, 0);

    setFormData(prev => ({
      ...prev,
      questionWise: updatedQuestionWise,
      obtained: totalObtained,
    }));
  };

  // Auto-calculate percentage and grade
  useEffect(() => {
    if (selectedAssessment && formData.obtained >= 0) {
      const percentage = (formData.obtained / selectedAssessment.maxMarks) * 100;
      
      // Determine grade based on percentage
      let grade = 'F';
      if (percentage >= 90) grade = 'A+';
      else if (percentage >= 80) grade = 'A';
      else if (percentage >= 70) grade = 'B+';
      else if (percentage >= 60) grade = 'B';
      else if (percentage >= 50) grade = 'C+';
      else if (percentage >= 40) grade = 'C';

      // Determine status
      let status = percentage >= (selectedAssessment.gradingScheme?.passingMarks || 40) ? 'Pass' : 'Fail';
      
      setFormData(prev => ({
        ...prev,
        grade,
        status: prev.status === 'Absent' || prev.status === 'Malpractice' ? prev.status : status,
      }));
    }
  }, [formData.obtained, selectedAssessment]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.assessmentId || !formData.studentRoll || !formData.studentName) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.obtained < 0 || formData.obtained > selectedAssessment?.maxMarks) {
      toast.error(`Obtained marks must be between 0 and ${selectedAssessment?.maxMarks}`);
      return;
    }

    // Validate question-wise marks if present
    if (formData.questionWise.length > 0) {
      const totalQuestionMarks = formData.questionWise.reduce((sum, q) => sum + q.marks, 0);
      if (Math.abs(totalQuestionMarks - formData.obtained) > 0.01) {
        toast.error('Total question-wise marks must equal obtained marks');
        return;
      }
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting result:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    {mode === 'create' ? 'Add Student Result' : 'Edit Student Result'}
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="assessmentId" className="block text-sm font-medium text-gray-700">
                        Assessment *
                      </label>
                      <select
                        id="assessmentId"
                        name="assessmentId"
                        value={formData.assessmentId}
                        onChange={handleAssessmentChange}
                        required
                        disabled={mode === 'edit'}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm disabled:bg-gray-100"
                      >
                        <option value="">Select Assessment</option>
                        {assessments.map((assessment) => (
                          <option key={assessment._id} value={assessment._id}>
                            {assessment.title} ({assessment.type}) - {assessment.course?.code}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="evaluationDate" className="block text-sm font-medium text-gray-700">
                        Evaluation Date *
                      </label>
                      <input
                        type="date"
                        id="evaluationDate"
                        name="evaluationDate"
                        value={formData.evaluationDate}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="studentRoll" className="block text-sm font-medium text-gray-700">
                        Student Roll Number *
                      </label>
                      <input
                        type="text"
                        id="studentRoll"
                        name="studentRoll"
                        value={formData.studentRoll}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="studentName" className="block text-sm font-medium text-gray-700">
                        Student Name *
                      </label>
                      <input
                        type="text"
                        id="studentName"
                        name="studentName"
                        value={formData.studentName}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  {/* Assessment Details */}
                  {selectedAssessment && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Assessment Details</h4>
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                        <div>
                          <span className="text-xs text-gray-500">Max Marks</span>
                          <p className="text-sm font-medium text-gray-900">{selectedAssessment.maxMarks}</p>
                        </div>
                        <div>
                          <span className="text-xs text-gray-500">Weightage</span>
                          <p className="text-sm font-medium text-gray-900">{selectedAssessment.weightage}%</p>
                        </div>
                        <div>
                          <span className="text-xs text-gray-500">Passing Marks</span>
                          <p className="text-sm font-medium text-gray-900">
                            {selectedAssessment.gradingScheme?.passingMarks || 40}%
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Marks and Status */}
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-4">
                    <div>
                      <label htmlFor="obtained" className="block text-sm font-medium text-gray-700">
                        Obtained Marks *
                      </label>
                      <input
                        type="number"
                        id="obtained"
                        name="obtained"
                        value={formData.obtained}
                        onChange={handleInputChange}
                        required
                        min="0"
                        max={selectedAssessment?.maxMarks || 100}
                        step="0.5"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                        Status *
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        {resultStatuses.map((status) => (
                          <option key={status} value={status}>
                            {status}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="grade" className="block text-sm font-medium text-gray-700">
                        Grade
                      </label>
                      <input
                        type="text"
                        id="grade"
                        name="grade"
                        value={formData.grade}
                        onChange={handleInputChange}
                        readOnly
                        className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Percentage
                      </label>
                      <div className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm sm:text-sm px-3 py-2">
                        {selectedAssessment 
                          ? `${((formData.obtained / selectedAssessment.maxMarks) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </div>
                    </div>
                  </div>

                  {/* Question-wise Marks */}
                  {formData.questionWise.length > 0 && (
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Question-wise Marks</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Question
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                CO
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Max Marks
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Obtained Marks
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {formData.questionWise.map((question, index) => (
                              <tr key={index}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  Q{question.q}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  CO{question.coIndex}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {question.maxMarks}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <input
                                    type="number"
                                    value={question.marks}
                                    onChange={(e) => handleQuestionMarksChange(index, e.target.value)}
                                    min="0"
                                    max={question.maxMarks}
                                    step="0.5"
                                    className="block w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                  />
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Remarks */}
                  <div>
                    <label htmlFor="remarks" className="block text-sm font-medium text-gray-700">
                      Remarks
                    </label>
                    <textarea
                      id="remarks"
                      name="remarks"
                      rows={3}
                      value={formData.remarks}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : mode === 'create' ? 'Add Result' : 'Update Result'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
