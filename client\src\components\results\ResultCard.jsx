import { useState } from 'react';
import {
  UserIcon,
  AcademicCapIcon,
  ChartBarIcon,
  CalendarIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EllipsisVerticalIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  MinusCircleIcon,
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const ResultCard = ({ result, onEdit, onViewCOAnalysis, onDelete }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusColor = (status) => {
    const colors = {
      'Pass': 'bg-green-100 text-green-800',
      'Fail': 'bg-red-100 text-red-800',
      'Absent': 'bg-gray-100 text-gray-800',
      'Malpractice': 'bg-orange-100 text-orange-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pass':
        return CheckCircleIcon;
      case 'Fail':
        return XCircleIcon;
      case 'Absent':
        return MinusCircleIcon;
      case 'Malpractice':
        return ExclamationTriangleIcon;
      default:
        return CheckCircleIcon;
    }
  };

  const getGradeColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const StatusIcon = getStatusIcon(result.status);
  const assessment = result.assessment;
  const course = assessment?.course;

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-900">
                {result.studentRoll}
              </span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {result.status}
              </span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 truncate" title={result.studentName}>
              {result.studentName}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {assessment?.title} - {course?.code}
            </p>
          </div>
          
          {/* Actions Menu */}
          <Menu as="div" className="relative inline-block text-left">
            <div>
              <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1">
                <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
              </Menu.Button>
            </div>

            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <div className="py-1">
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onViewCOAnalysis(result)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <EyeIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        View CO Analysis
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onEdit(result)}
                        className={classNames(
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <PencilIcon className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                        Edit Result
                      </button>
                    )}
                  </Menu.Item>
                  <div className="border-t border-gray-100" />
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onDelete(result._id)}
                        className={classNames(
                          active ? 'bg-red-50 text-red-900' : 'text-red-700',
                          'group flex items-center px-4 py-2 text-sm w-full text-left'
                        )}
                      >
                        <TrashIcon className="mr-3 h-4 w-4 text-red-400 group-hover:text-red-500" aria-hidden="true" />
                        Delete Result
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        {/* Result Details */}
        <div className="mt-4 space-y-3">
          {/* Marks and Percentage */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AcademicCapIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {result.obtained}/{assessment?.maxMarks} marks
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4 text-gray-400" />
              <span className={`text-sm font-medium ${getGradeColor(result.percentage)}`}>
                {result.percentage?.toFixed(1)}%
              </span>
            </div>
          </div>

          {/* Grade */}
          {result.grade && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Grade:</span>
              <span className={`text-sm font-medium ${getGradeColor(result.percentage)}`}>
                {result.grade}
              </span>
            </div>
          )}

          {/* CO Performance Summary */}
          {result.coPerformance && result.coPerformance.length > 0 && (
            <div>
              <div className="flex items-center space-x-1 mb-2">
                <span className="text-xs text-gray-500">CO Performance:</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {result.coPerformance.slice(0, 4).map((co, index) => (
                  <span
                    key={index}
                    className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                      co.attained 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    CO{co.coIndex}: {co.percentage?.toFixed(0)}%
                  </span>
                ))}
                {result.coPerformance.length > 4 && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    +{result.coPerformance.length - 4} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Question-wise Performance */}
          {result.questionWise && result.questionWise.length > 0 && (
            <div>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none"
              >
                {isExpanded ? 'Hide' : 'Show'} question-wise marks ({result.questionWise.length} questions)
              </button>
              {isExpanded && (
                <div className="mt-2 space-y-1">
                  {result.questionWise.map((q, index) => (
                    <div key={index} className="flex justify-between text-xs">
                      <span className="text-gray-600">Q{q.q} (CO{q.coIndex}):</span>
                      <span className={`font-medium ${
                        (q.marks / q.maxMarks) >= 0.5 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {q.marks}/{q.maxMarks}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Remarks */}
          {result.remarks && (
            <div className="mt-3">
              <span className="text-xs text-gray-500">Remarks:</span>
              <p className="text-sm text-gray-700 mt-1">{result.remarks}</p>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-4 w-4 text-gray-400" />
            <span className="text-xs text-gray-500">
              {result.evaluationDate 
                ? `Evaluated ${new Date(result.evaluationDate).toLocaleDateString()}`
                : `Added ${new Date(result.createdAt).toLocaleDateString()}`
              }
            </span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => onViewCOAnalysis(result)}
              className="text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              CO Analysis
            </button>
            <span className="text-gray-300">•</span>
            <button
              onClick={() => onEdit(result)}
              className="text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              Edit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
