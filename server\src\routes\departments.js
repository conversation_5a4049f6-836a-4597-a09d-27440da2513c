const express = require('express');
const {
  getDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deactivateDepartment,
  activateDepartment,
  getDepartmentFaculty,
  createDepartmentValidation,
  updateDepartmentValidation,
  handleValidationErrors,
} = require('../controllers/departmentController');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all departments
router.get('/', getDepartments);

// Get department by ID
router.get('/:id', getDepartmentById);

// Get department faculty
router.get('/:id/faculty', getDepartmentFaculty);

// Create new department (Admin only)
router.post('/', authorize('ADMIN'), createDepartmentValidation, handleValidationErrors, createDepartment);

// Update department (Admin only)
router.put('/:id', authorize('ADMIN'), updateDepartmentValidation, handleValidationErrors, updateDepartment);

// Deactivate department (Admin only)
router.put('/:id/deactivate', authorize('ADMIN'), deactivateDepartment);

// Activate department (Admin only)
router.put('/:id/activate', authorize('ADMIN'), activateDepartment);

module.exports = router;
