import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { coursesAPI, assessmentsAPI, resultsAPI, attainmentAPI } from '../../services/api';
import toast from 'react-hot-toast';
import {
  BookOpenIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  AcademicCapIcon,
  CalendarIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';



const quickActions = [
  {
    name: 'My Courses',
    description: 'View and manage your assigned courses',
    href: '/courses',
    icon: BookOpenIcon,
    color: 'bg-blue-500',
  },
  {
    name: 'Submit Assessments',
    description: 'Upload and manage assessment results',
    href: '/assessments',
    icon: ClipboardDocumentListIcon,
    color: 'bg-red-500',
  },
  {
    name: 'View Results',
    description: 'Check student performance and analytics',
    href: '/results',
    icon: ChartBarIcon,
    color: 'bg-green-500',
  },
  {
    name: 'Course Materials',
    description: 'Manage course content and resources',
    href: '/courses/materials',
    icon: DocumentTextIcon,
    color: 'bg-purple-500',
  },
];

export const FacultyDashboard = () => {
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState([
    { name: 'My Courses', value: '0', change: '+0', changeType: 'neutral', icon: BookOpenIcon },
    { name: 'Pending Assessments', value: '0', change: '+0', changeType: 'neutral', icon: ClipboardDocumentListIcon },
    { name: 'Students Enrolled', value: '0', change: '+0', changeType: 'neutral', icon: AcademicCapIcon },
    { name: 'Upcoming Deadlines', value: '0', change: '+0', changeType: 'neutral', icon: CalendarIcon },
  ]);
  const [recentActivities, setRecentActivities] = useState([]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Fetch courses assigned to faculty
      const coursesResponse = await coursesAPI.getCourses({
        facultyId: user._id,
        limit: 100
      });
      const courses = coursesResponse.data.data || [];

      // Fetch pending assessments
      const assessmentsResponse = await assessmentsAPI.getAssessments({
        facultyId: user._id,
        status: 'PENDING',
        limit: 100
      });
      const pendingAssessments = assessmentsResponse.data.data || [];

      // Calculate total students enrolled
      const totalStudents = courses.reduce((sum, course) => sum + (course.enrolledStudents || 0), 0);

      // Update stats
      setStats([
        {
          name: 'My Courses',
          value: courses.length.toString(),
          change: '+0',
          changeType: 'neutral',
          icon: BookOpenIcon
        },
        {
          name: 'Pending Assessments',
          value: pendingAssessments.length.toString(),
          change: '+0',
          changeType: pendingAssessments.length > 0 ? 'negative' : 'neutral',
          icon: ClipboardDocumentListIcon
        },
        {
          name: 'Students Enrolled',
          value: totalStudents.toString(),
          change: '+0',
          changeType: 'neutral',
          icon: AcademicCapIcon
        },
        {
          name: 'Upcoming Deadlines',
          value: '0',
          change: '+0',
          changeType: 'neutral',
          icon: CalendarIcon
        },
      ]);

      // Set recent activities (mock data for now)
      setRecentActivities([
        {
          id: 1,
          type: 'assessment',
          message: 'Assessment results submitted for Data Structures Lab',
          time: '2 hours ago',
          icon: ClipboardDocumentListIcon,
          color: 'bg-green-500'
        },
        {
          id: 2,
          type: 'course',
          message: 'New course material uploaded for Database Systems',
          time: '3 hours ago',
          icon: BookOpenIcon,
          color: 'bg-blue-500'
        },
        {
          id: 3,
          type: 'report',
          message: 'Student performance report generated for Database Systems',
          time: '4 hours ago',
          icon: ChartBarIcon,
          color: 'bg-purple-500'
        }
      ]);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 font-bold text-lg">
                  {user?.name?.charAt(0)?.toUpperCase()}
                </span>
              </div>
            </div>
            <div className="ml-4">
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user?.name}!
              </h1>
              <p className="text-gray-600">
                Manage your courses, assessments, and student progress.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item) => (
          <div key={item.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <item.icon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{item.name}</dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{item.value}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-5 py-3">
              <div className="text-sm">
                <span
                  className={`font-medium ${
                    item.changeType === 'positive'
                      ? 'text-green-600'
                      : item.changeType === 'negative'
                      ? 'text-red-600'
                      : 'text-gray-600'
                  }`}
                >
                  {item.change}
                </span>
                <span className="text-gray-500"> from last month</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              to={action.href}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <div>
                <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>
                  <action.icon className="h-6 w-6" aria-hidden="true" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  <span className="absolute inset-0" aria-hidden="true" />
                  {action.name}
                </h3>
                <p className="mt-2 text-sm text-gray-500">{action.description}</p>
              </div>
              <span
                className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400"
                aria-hidden="true"
              >
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="m11.293 17.293 1.414 1.414L19.414 12l-6.707-6.707-1.414 1.414L15.586 11H5v2h10.586l-4.293 4.293z" />
                </svg>
              </span>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Activity</h3>
          <div className="mt-5">
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.length > 0 ? (
                  recentActivities.map((activity, index) => (
                    <li key={activity.id}>
                      <div className={`relative ${index < recentActivities.length - 1 ? 'pb-8' : ''}`}>
                        {index < recentActivities.length - 1 && (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                        )}
                        <div className="relative flex space-x-3">
                          <div>
                            <span className={`h-8 w-8 rounded-full ${activity.color} flex items-center justify-center ring-8 ring-white`}>
                              <activity.icon className="h-5 w-5 text-white" aria-hidden="true" />
                            </span>
                          </div>
                          <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                              <p className="text-sm text-gray-500">
                                {activity.message}
                              </p>
                            </div>
                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                              {activity.time}
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li>
                    <div className="text-center py-8">
                      <p className="text-gray-500">No recent activity</p>
                    </div>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
