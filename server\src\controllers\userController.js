const mongoose = require('mongoose');
const { body, query } = require('express-validator');
const User = require('../models/User');
const Department = require('../models/Department');
const { AppError } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../utils/validation');
const { sendSuccess, sendNotFound } = require('../utils/response');

// Validation rules
const updateUserValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('role')
    .optional()
    .isIn(['ADMIN', 'DEPT_HEAD', 'FACULTY', 'AUDITOR'])
    .withMessage('Invalid role specified'),
  body('deptId')
    .optional()
    .isMongoId()
    .withMessage('Invalid department ID'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
];

// Get all users with filtering and pagination
const getUsers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      role,
      deptId,
      isActive,
      search,
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (role) filter.role = role;
    if (deptId) filter.deptId = deptId;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      filter.deptId = req.user.deptId;
    }

    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      User.find(filter)
        .populate('department', 'name code')
        .select('-passwordHash -refreshToken')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      User.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return sendSuccess(res, 'Users retrieved successfully', users, {
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return next(error);
  }
};

// Get user by ID
const getUserById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id)
      .populate('department', 'name code')
      .select('-passwordHash -refreshToken');

    if (!user) {
      return sendNotFound(res, 'User not found');
    }

    // Non-admin users can only view users from their department
    if (req.user.role !== 'ADMIN' && user.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    return sendSuccess(res, 'User retrieved successfully', user);
  } catch (error) {
    return next(error);
  }
};

// Update user (Admin and Dept Head only)
const updateUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, role, deptId, isActive, phone, designation } = req.body;

    const user = await User.findById(id);
    if (!user) {
      return sendNotFound(res, 'User not found');
    }

    // Department heads can only update users in their department
    if (req.user.role === 'DEPT_HEAD' && user.deptId.toString() !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    // Validate department if changing
    if (deptId && deptId !== user.deptId.toString()) {
      const department = await Department.findById(deptId);
      if (!department || !department.isActive) {
        return next(new AppError('Invalid or inactive department', 400));
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      id,
      {
        name,
        role,
        deptId,
        isActive,
        phone,
        designation,
        updatedBy: req.user._id,
      },
      { new: true, runValidators: true }
    ).populate('department', 'name code').select('-passwordHash -refreshToken');

    return sendSuccess(res, 'User updated successfully', updatedUser);
  } catch (error) {
    return next(error);
  }
};

// Deactivate user (Admin only)
const deactivateUser = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (id === req.user._id.toString()) {
      return next(new AppError('Cannot deactivate your own account', 400));
    }

    const user = await User.findByIdAndUpdate(
      id,
      { isActive: false, updatedBy: req.user._id },
      { new: true }
    ).select('-passwordHash -refreshToken');

    if (!user) {
      return sendNotFound(res, 'User not found');
    }

    return sendSuccess(res, 'User deactivated successfully', user);
  } catch (error) {
    return next(error);
  }
};

// Activate user (Admin only)
const activateUser = async (req, res, next) => {
  try {
    const { id } = req.params;

    const user = await User.findByIdAndUpdate(
      id,
      { isActive: true, updatedBy: req.user._id },
      { new: true }
    ).select('-passwordHash -refreshToken');

    if (!user) {
      return sendNotFound(res, 'User not found');
    }

    return sendSuccess(res, 'User activated successfully', user);
  } catch (error) {
    return next(error);
  }
};

// Get users by role
const getUsersByRole = async (req, res, next) => {
  try {
    const { role } = req.params;

    const filter = { role, isActive: true };

    // For non-admin users, filter by their department
    if (req.user.role !== 'ADMIN') {
      filter.deptId = req.user.deptId;
    }

    const users = await User.find(filter)
      .populate('department', 'name code')
      .select('name email role department phone designation')
      .sort({ name: 1 });

    return sendSuccess(res, `${role} users retrieved successfully`, users);
  } catch (error) {
    return next(error);
  }
};

// Get department statistics
const getDepartmentStats = async (req, res, next) => {
  try {
    const { deptId } = req.params;

    // Non-admin users can only view their department stats
    if (req.user.role !== 'ADMIN' && deptId !== req.user.deptId.toString()) {
      return next(new AppError('Access denied', 403));
    }

    const stats = await User.aggregate([
      { $match: { deptId: mongoose.Types.ObjectId(deptId), isActive: true } },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ]);

    const formattedStats = {
      total: 0,
      byRole: {},
    };

    stats.forEach(stat => {
      formattedStats.byRole[stat._id] = stat.count;
      formattedStats.total += stat.count;
    });

    return sendSuccess(res, 'Department statistics retrieved successfully', formattedStats);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  getUsers,
  getUserById,
  updateUser,
  deactivateUser,
  activateUser,
  getUsersByRole,
  getDepartmentStats,
  updateUserValidation,
  handleValidationErrors,
};
